# 🌱 Phase 4 : Spring Boot Portfolio (Semaines 33-50)

[![Phase Status](https://img.shields.io/badge/Status-Not%20Started-red?style=for-the-badge)](../docs/PROGRESS-TRACKER.md)
[![Projects](https://img.shields.io/badge/Projects-0/5-red?style=for-the-badge)](#projets-réalisés)
[![Duration](https://img.shields.io/badge/Duration-18%20weeks-blue?style=for-the-badge)](./README.md)

## 🎯 Objectifs de la Phase

À la fin de cette phase de 4.5 mois, je maîtrise :
- ✅ **Spring Boot Ecosystem** - IoC, DI, Auto-configuration, Starters
- ✅ **REST APIs complètes** - CRUD, validation, documentation, sécurité
- ✅ **Persistance moderne** - Spring Data JPA, repositories, transactions
- ✅ **Sécurité web** - Spring Security, JWT, OAuth2
- ✅ **Tests d'intégration** - TestContainers, MockMvc, WebTestClient
- ✅ **Déploiement moderne** - Docker, CI/CD, monitoring

## 📅 Planning Détaillé

| Période | Module | Focus Principal | Projet Livré | Statut |
|---------|--------|-----------------|---------------|---------|
| **S33-S35** | [Spring Intro](./week-33-35-spring-intro/) | IoC, DI, Spring Boot | [REST API Simple](./week-33-35-spring-intro/projects/15-rest-api-simple/) | ⏳ |
| **S36-S38** | [Spring Data](./week-36-38-spring-data/) | JPA, repositories | [Employee Management](./week-36-38-spring-data/projects/16-employee-management/) | ⏳ |
| **S39-S41** | [Security & Testing](./week-39-41-security-testing/) | Spring Security, tests | [E-commerce Backend](./week-39-41-security-testing/projects/17-ecommerce-backend/) | ⏳ |
| **S42-S44** | [Web & Deployment](./week-42-44-web-deployment/) | Thymeleaf, Docker | [Portfolio Website](./week-42-44-web-deployment/projects/18-portfolio-website/) | ⏳ |
| **S45-S48** | [Microservices](./week-45-48-microservices/) | Spring Cloud, APIs | [Microservices Demo](./week-45-48-microservices/projects/19-microservices-demo/) | ⏳ |
| **S49-S50** | [Optimization](./week-49-50-optimization/) | Performance, monitoring | Optimisation projets | ⏳ |

## 🏆 Projets Réalisés

### Applications Spring Boot (0/5)
| # | Projet | Concepts Clés | Lignes de Code | Tests | Démo |
|---|---------|---------------|----------------|--------|------|
| 15 | [REST API Simple](./week-33-35-spring-intro/projects/15-rest-api-simple/) | Spring Boot, REST | ~600 | 45 tests | ⏳ |
| 16 | [Employee Management](./week-36-38-spring-data/projects/16-employee-management/) | Spring Data, JPA | ~800 | 60 tests | ⏳ |
| 17 | [E-commerce Backend](./week-39-41-security-testing/projects/17-ecommerce-backend/) | Security, Tests | ~1200 | 90 tests | ⏳ |
| 18 | [Portfolio Website](./week-42-44-web-deployment/projects/18-portfolio-website/) | Web, Deployment | ~1000 | 75 tests | ⏳ |
| 19 | [Microservices Demo](./week-45-48-microservices/projects/19-microservices-demo/) | Microservices | ~1500 | 120 tests | ⏳ |

## 📊 Statistiques de la Phase

### Métriques de Code
- **Total lignes de code :** 0 / ~5,100 lignes Java
- **Tests écrits :** 0 / 390 tests unitaires/intégration
- **Couverture moyenne :** 0% / 85%+ objectif
- **APIs créées :** 0 / 15 endpoints REST

### Compétences Acquises
- **Spring Core :** ⭐⭐⭐⭐⭐ (0/5)
- **Spring Boot :** ⭐⭐⭐⭐⭐ (0/5)
- **Spring Data :** ⭐⭐⭐⭐⭐ (0/5)
- **Spring Security :** ⭐⭐⭐⭐⭐ (0/5)
- **REST APIs :** ⭐⭐⭐⭐⭐ (0/5)
- **Microservices :** ⭐⭐⭐⭐⭐ (0/5)

### Technologies Maîtrisées
- **Backend :** Spring Boot, Spring Data JPA, Spring Security
- **Base de données :** PostgreSQL, H2, Redis
- **Tests :** JUnit 5, Mockito, TestContainers
- **Déploiement :** Docker, Docker Compose
- **Monitoring :** Actuator, Micrometer, Prometheus

## 🔗 Navigation

### Modules de la Phase 4
- [Semaines 33-35: Spring Intro](./week-33-35-spring-intro/) - IoC, DI, Spring Boot basics
- [Semaines 36-38: Spring Data](./week-36-38-spring-data/) - JPA, repositories, transactions
- [Semaines 39-41: Security & Testing](./week-39-41-security-testing/) - Spring Security, tests avancés
- [Semaines 42-44: Web & Deployment](./week-42-44-web-deployment/) - Web apps, Docker
- [Semaines 45-48: Microservices](./week-45-48-microservices/) - Spring Cloud, architecture
- [Semaines 49-50: Optimization](./week-49-50-optimization/) - Performance, monitoring

### Autres Phases
- [📚 Phase 1: Fondamentaux](../phase-1-fundamentals/) - Syntaxe, POO, Collections
- [🚀 Phase 2: Java Avancé](../phase-2-advanced-java/) - Generics, Streams, Concurrence
- [📋 Phase 3: Préparation OCP](../phase-3-ocp-preparation/) - Examen et certification

## 📚 Ressources Utilisées

### Cours Principaux
- **Spring Academy** - Cours officiels Spring
- **Baeldung** - Tutorials Spring complets
- **Spring Boot in Action** - Craig Walls

### Documentation Officielle
- **Spring Framework Reference** - Documentation complète
- **Spring Boot Documentation** - Guides et références
- **Spring Security Reference** - Sécurité web

### Pratique & Projets
- **Spring Guides** - Getting Started guides
- **GitHub Spring Projects** - Exemples officiels
- **Awesome Spring** - Ressources communautaires

## ✅ Prérequis pour Emploi

Avant de postuler pour un poste développeur Java/Spring :

- [ ] **Spring Boot maîtrisé** - Applications complètes
- [ ] **REST APIs** - CRUD, validation, documentation
- [ ] **Persistance** - JPA, repositories, transactions
- [ ] **Sécurité** - Authentication, authorization
- [ ] **Tests** - Unitaires, intégration, end-to-end
- [ ] **Déploiement** - Docker, CI/CD
- [ ] **Portfolio** - 5 projets déployés et documentés

**🎯 Prêt pour l'emploi !** → Recherche de poste développeur Java/Spring

---

*Phase 4 en parallèle de Phase 3 - Portfolio employable en 18 semaines*
