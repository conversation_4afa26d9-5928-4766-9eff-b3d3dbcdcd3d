package com.ocpjourney;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Main Spring Boot application class.
 * 
 * This template provides a starting point for Spring Boot applications
 * in the Java OCP certification journey with proper configuration,
 * REST API setup, and testing infrastructure.
 * 
 * <AUTHOR> Student
 * @version 1.0.0
 * @since Java 17
 */
@SpringBootApplication
public class SpringBootTemplateApplication {

    /**
     * Main entry point of the Spring Boot application.
     * 
     * @param args command line arguments
     */
    public static void main(String[] args) {
        SpringApplication.run(SpringBootTemplateApplication.class, args);
    }
}
