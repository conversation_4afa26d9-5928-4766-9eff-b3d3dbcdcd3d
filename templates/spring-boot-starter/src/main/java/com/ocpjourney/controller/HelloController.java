package com.ocpjourney.controller;

import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * Sample REST controller demonstrating Spring Boot web capabilities.
 * 
 * This controller provides basic endpoints for testing and learning
 * Spring Boot REST API development.
 * 
 * <AUTHOR> Student
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/hello")
public class HelloController {

    /**
     * Simple hello endpoint.
     * 
     * @return greeting message
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> hello() {
        var response = Map.of(
            "message", "Hello from Spring Boot Template!",
            "timestamp", LocalDateTime.now(),
            "status", "success"
        );
        return ResponseEntity.ok(response);
    }

    /**
     * Personalized greeting endpoint.
     * 
     * @param name the name to greet
     * @return personalized greeting
     */
    @GetMapping("/{name}")
    public ResponseEntity<Map<String, Object>> helloName(@PathVariable String name) {
        var response = Map.of(
            "message", "Hello, " + name + "!",
            "timestamp", LocalDateTime.now(),
            "status", "success"
        );
        return ResponseEntity.ok(response);
    }

    /**
     * Echo endpoint that returns the posted message.
     * 
     * @param request the request body containing the message
     * @return echoed message with metadata
     */
    @PostMapping("/echo")
    public ResponseEntity<Map<String, Object>> echo(@RequestBody Map<String, String> request) {
        String message = request.getOrDefault("message", "No message provided");
        
        var response = Map.of(
            "original", message,
            "echo", "Echo: " + message,
            "length", message.length(),
            "timestamp", LocalDateTime.now(),
            "status", "success"
        );
        return ResponseEntity.ok(response);
    }
}
