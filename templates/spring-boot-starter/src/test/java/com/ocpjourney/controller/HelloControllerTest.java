package com.ocpjourney.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.hamcrest.Matchers.*;

/**
 * Unit tests for HelloController.
 * 
 * This class demonstrates Spring Boot web layer testing
 * using @WebMvcTest and MockMvc.
 * 
 * <AUTHOR> Student
 * @version 1.0.0
 */
@WebMvcTest(HelloController.class)
class HelloControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void shouldReturnHelloMessage() throws Exception {
        mockMvc.perform(get("/api/hello"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.message", is("Hello from Spring Boot Template!")))
                .andExpect(jsonPath("$.status", is("success")))
                .andExpect(jsonPath("$.timestamp", notNullValue()));
    }

    @Test
    void shouldReturnPersonalizedGreeting() throws Exception {
        String name = "John";
        
        mockMvc.perform(get("/api/hello/{name}", name))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.message", is("Hello, " + name + "!")))
                .andExpect(jsonPath("$.status", is("success")))
                .andExpect(jsonPath("$.timestamp", notNullValue()));
    }

    @Test
    void shouldEchoMessage() throws Exception {
        Map<String, String> request = Map.of("message", "Test message");
        String requestJson = objectMapper.writeValueAsString(request);
        
        mockMvc.perform(post("/api/hello/echo")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.original", is("Test message")))
                .andExpect(jsonPath("$.echo", is("Echo: Test message")))
                .andExpect(jsonPath("$.length", is(12)))
                .andExpect(jsonPath("$.status", is("success")))
                .andExpect(jsonPath("$.timestamp", notNullValue()));
    }

    @Test
    void shouldHandleEmptyEchoMessage() throws Exception {
        Map<String, String> request = Map.of();
        String requestJson = objectMapper.writeValueAsString(request);
        
        mockMvc.perform(post("/api/hello/echo")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.original", is("No message provided")))
                .andExpect(jsonPath("$.echo", is("Echo: No message provided")))
                .andExpect(jsonPath("$.status", is("success")));
    }
}
