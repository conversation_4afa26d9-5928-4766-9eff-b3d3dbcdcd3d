package com.ocpjourney;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * Integration tests for the Spring Boot application.
 * 
 * This class tests the application context loading and basic
 * Spring Boot functionality.
 * 
 * <AUTHOR> Student
 * @version 1.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
class SpringBootTemplateApplicationTests {

    /**
     * Test that the Spring application context loads successfully.
     */
    @Test
    void contextLoads() {
        // This test will fail if the application context cannot be loaded
        // It's a basic smoke test for Spring Boot applications
    }
}
