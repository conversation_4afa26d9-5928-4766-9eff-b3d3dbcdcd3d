# Spring Boot Configuration
spring:
  application:
    name: spring-boot-template
  
  # Profile-specific configuration
  profiles:
    active: dev
  
  # Database Configuration
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA Configuration
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  
  # H2 Console (for development)
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # Jackson Configuration
  jackson:
    serialization:
      write-dates-as-timestamps: false
    time-zone: UTC

# Server Configuration
server:
  port: 8080
  servlet:
    context-path: /
  error:
    include-message: always
    include-binding-errors: always

# Management/Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# Logging Configuration
logging:
  level:
    com.ocpjourney: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev
  
  # Development-specific database
  datasource:
    url: jdbc:h2:mem:devdb
  
  # Enable H2 console
  h2:
    console:
      enabled: true

# Development server configuration
server:
  port: 8080

logging:
  level:
    root: INFO
    com.ocpjourney: DEBUG

---
# Test Profile
spring:
  config:
    activate:
      on-profile: test
  
  # Test-specific database
  datasource:
    url: jdbc:h2:mem:testdb
  
  jpa:
    hibernate:
      ddl-auto: create-drop

# Test server configuration
server:
  port: 0  # Random port for tests

logging:
  level:
    root: WARN
    com.ocpjourney: INFO

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod
  
  # Production database (example with PostgreSQL)
  # datasource:
  #   url: ***************************************
  #   username: ${DB_USERNAME:user}
  #   password: ${DB_PASSWORD:password}
  #   driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

# Production server configuration
server:
  port: ${PORT:8080}

# Disable H2 console in production
  h2:
    console:
      enabled: false

logging:
  level:
    root: WARN
    com.ocpjourney: INFO
