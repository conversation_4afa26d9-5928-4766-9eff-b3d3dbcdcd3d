package com.ocpjourney;

import org.junit.jupiter.api.*;
import org.junit.jupiter.api.io.TempDir;
import static org.assertj.core.api.Assertions.*;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.nio.file.Path;

/**
 * Test class for the Main application.
 * 
 * This class demonstrates various JUnit 5 testing features and best practices
 * for testing Java applications in the OCP certification journey.
 * 
 * <AUTHOR> Student
 * @version 1.0.0
 */
@DisplayName("Main Application Tests")
class MainTest {
    
    private final ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    private final PrintStream originalOut = System.out;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        // Redirect System.out to capture output for testing
        System.setOut(new PrintStream(outputStream));
    }
    
    @AfterEach
    void tearDown() {
        // Restore original System.out
        System.setOut(originalOut);
    }
    
    @Test
    @DisplayName("Should run main method without exceptions")
    void shouldRunMainMethodWithoutExceptions() {
        // Given
        String[] args = {};
        
        // When & Then
        assertThatCode(() -> Main.main(args))
                .doesNotThrowAnyException();
    }
    
    @Test
    @DisplayName("Should display Java version information")
    void shouldDisplayJavaVersionInformation() {
        // Given
        String[] args = {};
        
        // When
        Main.main(args);
        String output = outputStream.toString();
        
        // Then
        assertThat(output)
                .contains("Java Runtime Information")
                .contains("Java Version:")
                .contains("Java Vendor:")
                .contains("Java Home:");
    }
    
    @Test
    @DisplayName("Should display project template title")
    void shouldDisplayProjectTemplateTitle() {
        // Given
        String[] args = {};
        
        // When
        Main.main(args);
        String output = outputStream.toString();
        
        // Then
        assertThat(output)
                .contains("🚀 Java OCP Project Template")
                .contains("============================");
    }
    
    @Test
    @DisplayName("Should demonstrate basic operations")
    void shouldDemonstrateBasicOperations() {
        // Given
        String[] args = {};
        
        // When
        Main.main(args);
        String output = outputStream.toString();
        
        // Then
        assertThat(output)
                .contains("Basic Operations")
                .contains("10 + 20 = 30")
                .contains("Hello, Java 17!")
                .contains("Message length:");
    }
    
    @Test
    @DisplayName("Should demonstrate collections usage")
    void shouldDemonstrateCollectionsUsage() {
        // Given
        String[] args = {};
        
        // When
        Main.main(args);
        String output = outputStream.toString();
        
        // Then
        assertThat(output)
                .contains("Collections:")
                .contains("Numbers:")
                .contains("Even numbers:");
    }
    
    @Test
    @DisplayName("Should demonstrate modern Java features")
    void shouldDemonstrateModernJavaFeatures() {
        // Given
        String[] args = {};
        
        // When
        Main.main(args);
        String output = outputStream.toString();
        
        // Then
        assertThat(output)
                .contains("Modern Java Features")
                .contains("Text block preview:")
                .contains("Pattern matching result:")
                .contains("Record example:");
    }
    
    @Test
    @DisplayName("Should complete execution successfully")
    void shouldCompleteExecutionSuccessfully() {
        // Given
        String[] args = {};
        
        // When
        Main.main(args);
        String output = outputStream.toString();
        
        // Then
        assertThat(output)
                .contains("✅ Template execution completed successfully!");
    }
    
    @Nested
    @DisplayName("Java System Properties Tests")
    class JavaSystemPropertiesTests {
        
        @Test
        @DisplayName("Should have Java 17 or higher")
        void shouldHaveJava17OrHigher() {
            String javaVersion = System.getProperty("java.version");
            
            assertThat(javaVersion)
                    .isNotNull()
                    .satisfiesAnyOf(
                            version -> assertThat(version).startsWith("17"),
                            version -> assertThat(version).startsWith("18"),
                            version -> assertThat(version).startsWith("19"),
                            version -> assertThat(version).startsWith("20"),
                            version -> assertThat(version).startsWith("21")
                    );
        }
        
        @Test
        @DisplayName("Should have valid Java home directory")
        void shouldHaveValidJavaHomeDirectory() {
            String javaHome = System.getProperty("java.home");
            
            assertThat(javaHome)
                    .isNotNull()
                    .isNotEmpty();
        }
        
        @Test
        @DisplayName("Should have valid OS information")
        void shouldHaveValidOSInformation() {
            String osName = System.getProperty("os.name");
            String osVersion = System.getProperty("os.version");
            
            assertThat(osName).isNotNull().isNotEmpty();
            assertThat(osVersion).isNotNull().isNotEmpty();
        }
    }
    
    @Nested
    @DisplayName("Modern Java Features Tests")
    class ModernJavaFeaturesTests {
        
        @Test
        @DisplayName("Should support text blocks")
        void shouldSupportTextBlocks() {
            var textBlock = """
                    Line 1
                    Line 2
                    Line 3
                    """;
            
            assertThat(textBlock)
                    .contains("Line 1")
                    .contains("Line 2")
                    .contains("Line 3");
        }
        
        @Test
        @DisplayName("Should support pattern matching for instanceof")
        void shouldSupportPatternMatchingForInstanceof() {
            Object obj = "Test String";
            
            if (obj instanceof String str) {
                assertThat(str).isEqualTo("Test String");
            } else {
                fail("Pattern matching for instanceof not working");
            }
        }
        
        @Test
        @DisplayName("Should support records")
        void shouldSupportRecords() {
            record TestRecord(String name, int value) {}
            
            var record = new TestRecord("test", 42);
            
            assertThat(record.name()).isEqualTo("test");
            assertThat(record.value()).isEqualTo(42);
            assertThat(record.toString()).contains("test").contains("42");
        }
        
        @Test
        @DisplayName("Should support var keyword")
        void shouldSupportVarKeyword() {
            var stringVar = "Hello";
            var intVar = 42;
            var listVar = java.util.List.of(1, 2, 3);
            
            assertThat(stringVar).isInstanceOf(String.class);
            assertThat(intVar).isInstanceOf(Integer.class);
            assertThat(listVar).isInstanceOf(java.util.List.class);
        }
    }
    
    @RepeatedTest(3)
    @DisplayName("Should run consistently across multiple executions")
    void shouldRunConsistentlyAcrossMultipleExecutions() {
        // Given
        String[] args = {};
        
        // When
        Main.main(args);
        String output = outputStream.toString();
        
        // Then
        assertThat(output)
                .contains("🚀 Java OCP Project Template")
                .contains("✅ Template execution completed successfully!");
        
        // Clear output for next iteration
        outputStream.reset();
    }
}
