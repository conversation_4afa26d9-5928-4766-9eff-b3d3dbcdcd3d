{
    // Configuration Java pour VS Code
    "java.configuration.updateBuildConfiguration": "automatic",
    "java.compile.nullAnalysis.mode": "automatic",
    "java.completion.enabled": true,
    "java.completion.overwrite": true,
    
    // Formatage du code (Google Java Style)
    "java.format.enabled": true,
    "java.format.settings.url": "https://raw.githubusercontent.com/google/styleguide/gh-pages/eclipse-java-google-style.xml",
    "editor.formatOnSave": true,
    
    // Tests JUnit
    "java.test.defaultConfig": "",
    "java.test.config": {
        "workingDirectory": "${workspaceFolder}"
    },
    
    // Maven
    "maven.executable.path": "mvn",
    "maven.terminal.useJavaHome": true,
    
    // Debugging
    "java.debug.settings.enableRunDebugCodeLens": true,
    "java.debug.settings.showHex": false,
    "java.debug.settings.showStaticVariables": false,
    "java.debug.settings.showQualifiedNames": false,
    
    // Éditeur
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,
    "editor.rulers": [100],
    "editor.wordWrap": "wordWrapColumn",
    "editor.wordWrapColumn": 100,
    
    // Fichiers
    "files.exclude": {
        "**/target": true,
        "**/.classpath": true,
        "**/.project": true,
        "**/.settings": true,
        "**/.factorypath": true
    },
    
    // Recherche
    "search.exclude": {
        "**/target": true,
        "**/node_modules": true
    },
    
    // Extensions recommandées pour ce projet
    "extensions.recommendations": [
        "vscjava.vscode-java-pack",
        "redhat.vscode-xml",
        "ms-vscode.vscode-json",
        "eamodio.gitlens"
    ]
}
