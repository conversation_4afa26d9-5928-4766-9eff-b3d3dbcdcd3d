{"version": "0.2.0", "configurations": [{"type": "java", "name": "Run Main", "request": "launch", "mainClass": "com.ocpjourney.Main", "projectName": "maven-java-project", "args": "", "console": "integratedTerminal"}, {"type": "java", "name": "Debug Main", "request": "launch", "mainClass": "com.ocpjourney.Main", "projectName": "maven-java-project", "args": "", "console": "integratedTerminal", "stopOnEntry": false}, {"type": "java", "name": "Run Tests", "request": "launch", "mainClass": "", "projectName": "maven-java-project", "preLaunchTask": "maven: test", "console": "integratedTerminal"}]}