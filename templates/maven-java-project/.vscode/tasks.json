{"version": "2.0.0", "tasks": [{"label": "maven: compile", "type": "shell", "command": "mvn", "args": ["compile"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$javac"}, {"label": "maven: test", "type": "shell", "command": "mvn", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$javac"}, {"label": "maven: package", "type": "shell", "command": "mvn", "args": ["package"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$javac"}, {"label": "maven: clean", "type": "shell", "command": "mvn", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "maven: test with coverage", "type": "shell", "command": "mvn", "args": ["clean", "test", "jacoco:report"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$javac"}]}