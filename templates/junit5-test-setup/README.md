# 🧪 JUnit 5 Test Setup Template

[![JUnit](https://img.shields.io/badge/JUnit-5-green?style=for-the-badge&logo=junit5)](https://junit.org/junit5/)
[![AssertJ](https://img.shields.io/badge/AssertJ-3.24-blue?style=for-the-badge)](https://assertj.github.io/doc/)
[![<PERSON><PERSON>to](https://img.shields.io/badge/Mockito-5.5-orange?style=for-the-badge)](https://site.mockito.org/)

## 📋 Description

Template complet pour la configuration et l'utilisation de JUnit 5 dans les projets Java OCP. Ce template inclut :

- **Configuration JUnit 5** complète avec extensions
- **Exemples de tests** pour tous les types de testing
- **Bonnes pratiques** de test unitaire
- **Intégration AssertJ** pour des assertions expressives
- **Setup Mockito** pour le mocking
- **Patterns de test** avancés

## 🚀 Utilisation

### 1. Copier les Dépendances
Ajouter dans votre `pom.xml` :

```xml
<dependencies>
    <!-- JUnit 5 -->
    <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>5.10.0</version>
        <scope>test</scope>
    </dependency>
    
    <!-- AssertJ -->
    <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <version>3.24.2</version>
        <scope>test</scope>
    </dependency>
    
    <!-- Mockito -->
    <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>5.5.0</version>
        <scope>test</scope>
    </dependency>
</dependencies>
```

### 2. Copier les Classes d'Exemple
```bash
# Copier les exemples de test vers votre projet
cp templates/junit5-test-setup/src/test/java/* src/test/java/
```

### 3. Adapter à Votre Projet
- Modifier les packages selon votre structure
- Adapter les exemples à vos classes métier
- Personnaliser les configurations de test

## 📁 Structure du Template

```
junit5-test-setup/
├── README.md                                    # Ce fichier
├── pom-dependencies.xml                         # Dépendances à copier
└── src/test/java/com/ocpjourney/examples/
    ├── BasicTestExamples.java                   # Tests de base JUnit 5
    ├── ParameterizedTestExamples.java           # Tests paramétrés
    ├── NestedTestExamples.java                  # Tests imbriqués
    ├── MockitoTestExamples.java                 # Tests avec mocks
    ├── AssertJExamples.java                     # Assertions AssertJ
    ├── TestLifecycleExamples.java               # Cycle de vie des tests
    ├── ConditionalTestExamples.java             # Tests conditionnels
    ├── TimeoutTestExamples.java                 # Tests avec timeout
    ├── ExceptionTestExamples.java               # Tests d'exceptions
    └── CustomExtensionExample.java              # Extensions personnalisées
```

## 🧪 Types de Tests Inclus

### 1. Tests de Base
- **@Test** - Tests unitaires simples
- **@DisplayName** - Noms descriptifs
- **@BeforeEach/@AfterEach** - Setup/teardown
- **Assertions basiques** - assertEquals, assertTrue, etc.

### 2. Tests Paramétrés
- **@ParameterizedTest** - Tests avec paramètres
- **@ValueSource** - Sources de valeurs simples
- **@CsvSource** - Sources CSV
- **@MethodSource** - Sources de méthodes

### 3. Tests Imbriqués
- **@Nested** - Organisation hiérarchique
- **Contextes de test** - Groupement logique
- **Setup spécialisé** - Configuration par contexte

### 4. Tests avec Mocks
- **@Mock** - Objets mockés
- **@InjectMocks** - Injection de mocks
- **Stubbing** - Configuration des comportements
- **Verification** - Vérification des interactions

### 5. Assertions Avancées
- **AssertJ fluent API** - Assertions expressives
- **Assertions personnalisées** - Validations métier
- **Soft assertions** - Multiples assertions
- **Exception assertions** - Tests d'erreurs

## 📚 Exemples de Code

### Test Unitaire Simple
```java
@Test
@DisplayName("Should calculate sum correctly")
void shouldCalculateSumCorrectly() {
    // Given
    Calculator calculator = new Calculator();
    
    // When
    int result = calculator.add(2, 3);
    
    // Then
    assertThat(result).isEqualTo(5);
}
```

### Test Paramétré
```java
@ParameterizedTest
@ValueSource(ints = {1, 2, 3, 4, 5})
@DisplayName("Should return true for positive numbers")
void shouldReturnTrueForPositiveNumbers(int number) {
    assertThat(NumberUtils.isPositive(number)).isTrue();
}
```

### Test avec Mock
```java
@Test
@DisplayName("Should save user successfully")
void shouldSaveUserSuccessfully() {
    // Given
    User user = new User("John", "<EMAIL>");
    when(userRepository.save(any(User.class))).thenReturn(user);
    
    // When
    User savedUser = userService.save(user);
    
    // Then
    assertThat(savedUser.getName()).isEqualTo("John");
    verify(userRepository).save(user);
}
```

## 🎯 Bonnes Pratiques Incluses

### Nommage des Tests
- **Méthodes descriptives** - `shouldReturnTrueWhenInputIsValid()`
- **@DisplayName** - Descriptions en langage naturel
- **Organisation logique** - Groupement par fonctionnalité

### Structure Given-When-Then
```java
@Test
void shouldProcessOrderSuccessfully() {
    // Given - Setup des données de test
    Order order = new Order(items);
    
    // When - Exécution de l'action testée
    OrderResult result = orderService.process(order);
    
    // Then - Vérification des résultats
    assertThat(result.isSuccess()).isTrue();
}
```

### Isolation des Tests
- **@BeforeEach** - Setup propre pour chaque test
- **Mocks indépendants** - Pas de partage d'état
- **Données de test** - Création locale

### Assertions Expressives
```java
// AssertJ - Plus lisible que JUnit assertions
assertThat(users)
    .hasSize(3)
    .extracting(User::getName)
    .containsExactly("Alice", "Bob", "Charlie");
```

## 🔧 Configuration Avancée

### Extensions JUnit 5
```java
@ExtendWith({MockitoExtension.class, TempDirectoryExtension.class})
class AdvancedTest {
    // Configuration automatique des mocks et répertoires temporaires
}
```

### Configuration Globale
```properties
# junit-platform.properties
junit.jupiter.execution.parallel.enabled=true
junit.jupiter.execution.parallel.mode.default=concurrent
```

### Profils de Test
```xml
<!-- Profile pour tests rapides -->
<profile>
    <id>fast-tests</id>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <excludedGroups>slow</excludedGroups>
                </configuration>
            </plugin>
        </plugins>
    </build>
</profile>
```

## 📊 Métriques et Rapports

### Couverture de Code
```bash
# Génération rapport JaCoCo
mvn test jacoco:report

# Vérification seuil de couverture
mvn jacoco:check
```

### Rapports de Test
```bash
# Exécution avec rapport détaillé
mvn test -Dmaven.test.failure.ignore=true

# Tests avec tags spécifiques
mvn test -Dgroups="unit,integration"
```

## 🚀 Commandes Utiles

### Exécution des Tests
```bash
# Tous les tests
mvn test

# Tests spécifiques
mvn test -Dtest=UserServiceTest

# Tests par pattern
mvn test -Dtest="*ServiceTest"

# Tests avec profil
mvn test -P fast-tests
```

### Debug des Tests
```bash
# Mode debug
mvn test -Dmaven.surefire.debug

# Verbose output
mvn test -X

# Tests en parallèle
mvn test -Dparallel=methods
```

## 📖 Ressources d'Apprentissage

### Documentation Officielle
- [JUnit 5 User Guide](https://junit.org/junit5/docs/current/user-guide/)
- [AssertJ Documentation](https://assertj.github.io/doc/)
- [Mockito Documentation](https://javadoc.io/doc/org.mockito/mockito-core/latest/org/mockito/Mockito.html)

### Tutoriels Recommandés
- [Baeldung JUnit 5](https://www.baeldung.com/junit-5)
- [Testing with Spring Boot](https://spring.io/guides/gs/testing-web/)
- [Modern Java Testing](https://www.petrikainulainen.net/programming/testing/)

---

**Template créé pour :** Java OCP Certification Journey
**Version :** 1.0.0
**Dernière mise à jour :** [Date]
