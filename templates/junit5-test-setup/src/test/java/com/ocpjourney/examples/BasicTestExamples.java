package com.ocpjourney.examples;

import org.junit.jupiter.api.*;
import static org.assertj.core.api.Assertions.*;

/**
 * Examples of basic JUnit 5 test patterns and best practices.
 * 
 * This class demonstrates fundamental testing concepts including:
 * - Basic test structure
 * - Test lifecycle methods
 * - Assertions with AssertJ
 * - Test organization
 * 
 * <AUTHOR> Student
 * @version 1.0.0
 */
@DisplayName("Basic Test Examples")
class BasicTestExamples {
    
    private Calculator calculator;
    
    @BeforeAll
    static void setUpClass() {
        System.out.println("🚀 Starting BasicTestExamples test suite");
    }
    
    @AfterAll
    static void tearDownClass() {
        System.out.println("✅ Completed BasicTestExamples test suite");
    }
    
    @BeforeEach
    void setUp() {
        calculator = new Calculator();
        System.out.println("📝 Setting up test with new Calculator instance");
    }
    
    @AfterEach
    void tearDown() {
        calculator = null;
        System.out.println("🧹 Cleaning up after test");
    }
    
    @Test
    @DisplayName("Should add two positive numbers correctly")
    void shouldAddTwoPositiveNumbersCorrectly() {
        // Given
        int a = 5;
        int b = 3;
        
        // When
        int result = calculator.add(a, b);
        
        // Then
        assertThat(result).isEqualTo(8);
    }
    
    @Test
    @DisplayName("Should subtract smaller from larger number")
    void shouldSubtractSmallerFromLargerNumber() {
        // Given
        int a = 10;
        int b = 4;
        
        // When
        int result = calculator.subtract(a, b);
        
        // Then
        assertThat(result)
                .isEqualTo(6)
                .isPositive()
                .isGreaterThan(0);
    }
    
    @Test
    @DisplayName("Should multiply two numbers correctly")
    void shouldMultiplyTwoNumbersCorrectly() {
        // Given
        int a = 7;
        int b = 6;
        
        // When
        int result = calculator.multiply(a, b);
        
        // Then
        assertThat(result).isEqualTo(42);
    }
    
    @Test
    @DisplayName("Should divide two numbers correctly")
    void shouldDivideTwoNumbersCorrectly() {
        // Given
        double a = 15.0;
        double b = 3.0;
        
        // When
        double result = calculator.divide(a, b);
        
        // Then
        assertThat(result)
                .isEqualTo(5.0)
                .isCloseTo(5.0, within(0.001));
    }
    
    @Test
    @DisplayName("Should handle zero addition correctly")
    void shouldHandleZeroAdditionCorrectly() {
        // Given
        int a = 5;
        int b = 0;
        
        // When
        int result = calculator.add(a, b);
        
        // Then
        assertThat(result).isEqualTo(a);
    }
    
    @Test
    @DisplayName("Should handle negative numbers in addition")
    void shouldHandleNegativeNumbersInAddition() {
        // Given
        int a = -5;
        int b = 3;
        
        // When
        int result = calculator.add(a, b);
        
        // Then
        assertThat(result)
                .isEqualTo(-2)
                .isNegative();
    }
    
    @Test
    @DisplayName("Should return zero when subtracting same numbers")
    void shouldReturnZeroWhenSubtractingSameNumbers() {
        // Given
        int a = 7;
        int b = 7;
        
        // When
        int result = calculator.subtract(a, b);
        
        // Then
        assertThat(result).isZero();
    }
    
    @Test
    @DisplayName("Should return zero when multiplying by zero")
    void shouldReturnZeroWhenMultiplyingByZero() {
        // Given
        int a = 42;
        int b = 0;
        
        // When
        int result = calculator.multiply(a, b);
        
        // Then
        assertThat(result).isZero();
    }
    
    /**
     * Simple Calculator class for testing purposes.
     */
    static class Calculator {
        
        public int add(int a, int b) {
            return a + b;
        }
        
        public int subtract(int a, int b) {
            return a - b;
        }
        
        public int multiply(int a, int b) {
            return a * b;
        }
        
        public double divide(double a, double b) {
            if (b == 0) {
                throw new IllegalArgumentException("Division by zero is not allowed");
            }
            return a / b;
        }
    }
}
