# ⚡ Projets Core Java - Niveau 2

[![Level](https://img.shields.io/badge/Level-2%20Core%20Java-orange?style=for-the-badge)](../README.md)
[![Projects](https://img.shields.io/badge/Projects-0/7-red?style=for-the-badge)](#projets-du-niveau)
[![Phase](https://img.shields.io/badge/Phase-2%20Advanced-blue?style=for-the-badge)](../../phase-2-advanced-java/)

## 🎯 Objectifs du Niveau

Ce niveau développe l'**expertise technique avancée** en Java SE 17. Les 7 projets permettent de maîtriser :

- **Generics avancés** - Types paramétrés, wildcards, PECS principle
- **Streams & Lambdas** - API Streams complète, programmation fonctionnelle
- **I/O & NIO.2** - Files, Paths, manipulation fichiers moderne
- **Concurrence** - Threads, ExecutorService, synchronisation, atomic classes
- **Modules (JPMS)** - Java Platform Module System, encapsulation forte
- **Architecture** - Design patterns, clean architecture
- **Performance** - Optimisation, profiling, bonnes pratiques

## 📋 Projets du Niveau

### Progression Actuelle (0/7)
| # | Projet | Statut | Concepts Clés | Lignes | Tests | Durée |
|---|---------|---------|---------------|--------|-------|-------|
| 8 | [Cache LRU](./08-lru-cache/) | ⏳ | Generics, Collections | ~400 | 35 | 3 sem |
| 9 | [Analyseur CSV](./09-csv-analyzer/) | ⏳ | Streams, Lambdas | ~500 | 40 | 3 sem |
| 10 | [Analyseur Logs](./10-log-analyzer/) | ⏳ | I/O, NIO.2, Streams | ~600 | 45 | 3 sem |
| 11 | [Backup Manager](./11-backup-manager/) | ⏳ | Concurrence, I/O | ~700 | 50 | 3 sem |
| 12 | [Chat Server](./12-chat-server/) | ⏳ | Modules, Networking | ~800 | 55 | 3 sem |
| 13 | [Application Modulaire](./13-modular-application/) | ⏳ | JPMS, Architecture | ~900 | 60 | 3 sem |
| 14 | [Mini Framework](./14-mini-framework/) | ⏳ | Design Patterns | ~1000 | 70 | 1 sem |

### Métriques Globales
- **Total lignes de code :** 0 / 4,900 lignes Java
- **Total tests :** 0 / 355 tests unitaires
- **Couverture moyenne :** 0% / 85%+ objectif
- **Durée totale :** 0 / 19 semaines

## 🚀 Parcours d'Apprentissage

### Projet 8 : Cache LRU Générique
**Semaines 17-19 | Concepts : Generics, Collections avancées**

Implémentation d'un cache LRU (Least Recently Used) générique et thread-safe.

**Fonctionnalités :**
- Cache générique avec types paramétrés
- Politique d'éviction LRU
- Thread-safety avec synchronisation
- Métriques de performance

**Apprentissages :**
- Generics avancés et wildcards
- LinkedHashMap et ordre d'insertion
- Synchronisation et thread-safety
- Design patterns (Strategy, Observer)

### Projet 9 : Analyseur CSV
**Semaines 20-22 | Concepts : Streams, Lambdas, Functional Programming**

Analyseur de fichiers CSV avec API Streams pour traitement de données.

**Fonctionnalités :**
- Parsing CSV configurable
- Transformations avec Streams
- Agrégations et statistiques
- Export vers différents formats

**Apprentissages :**
- API Streams complète
- Expressions lambda et method references
- Collectors personnalisés
- Programmation fonctionnelle

### Projet 10 : Analyseur de Logs
**Semaines 23-25 | Concepts : I/O, NIO.2, Streams**

Analyseur de logs système avec NIO.2 pour performance et Streams pour traitement.

**Fonctionnalités :**
- Lecture fichiers volumineux avec NIO.2
- Parsing de logs multi-formats
- Filtrage et recherche avancée
- Rapports et visualisations

**Apprentissages :**
- Files et Paths API
- Streams de fichiers
- Pattern matching et regex
- Gestion mémoire pour gros fichiers

### Projet 11 : Backup Manager
**Semaines 26-28 | Concepts : Concurrence, I/O, Scheduling**

Gestionnaire de sauvegardes avec traitement concurrent et scheduling.

**Fonctionnalités :**
- Sauvegardes incrémentales et complètes
- Traitement concurrent multi-thread
- Scheduling automatique
- Compression et chiffrement

**Apprentissages :**
- ExecutorService et thread pools
- CompletableFuture et programmation asynchrone
- Synchronisation avancée (locks, barriers)
- Gestion des ressources

### Projet 12 : Chat Server
**Semaines 29-31 | Concepts : Modules, Networking, Concurrence**

Serveur de chat modulaire avec architecture JPMS et networking NIO.

**Fonctionnalités :**
- Architecture modulaire JPMS
- Serveur NIO non-bloquant
- Protocole de communication custom
- Gestion des salles et utilisateurs

**Apprentissages :**
- Java Platform Module System
- NIO et Selectors
- Protocoles réseau
- Architecture modulaire

### Projet 13 : Application Modulaire
**Semaines 32 | Concepts : JPMS, Architecture, Services**

Application complète démontrant l'architecture modulaire Java.

**Fonctionnalités :**
- Modules découplés avec services
- Plugin system dynamique
- Configuration externalisée
- Monitoring et métriques

**Apprentissages :**
- Services et ServiceLoader
- Module path vs classpath
- Encapsulation forte
- Architecture hexagonale

### Projet 14 : Mini Framework
**Semaines 32 | Concepts : Design Patterns, Reflection, Annotations**

Framework léger démontrant les design patterns et la métaprogrammation.

**Fonctionnalités :**
- Injection de dépendances
- Annotations personnalisées
- Proxy dynamiques
- Configuration par convention

**Apprentissages :**
- Design patterns avancés
- Reflection et introspection
- Annotations et processing
- Architecture de framework

## 📊 Standards de Qualité

### Code Avancé
- **Design Patterns** - Application appropriée et justifiée
- **Performance** - Optimisation et profiling
- **Thread Safety** - Concurrence sûre et efficace
- **Memory Management** - Gestion mémoire optimale

### Tests Avancés
- **Couverture :** 85%+ avec tests de performance
- **Concurrence :** Tests multi-thread et race conditions
- **Integration :** Tests d'intégration complexes
- **Benchmarking :** JMH pour tests de performance

### Architecture
- **Modularité :** Séparation claire des responsabilités
- **Extensibilité :** Code facilement extensible
- **Maintenabilité :** Architecture claire et documentée
- **Scalabilité :** Performance sous charge

### Documentation Technique
- **Architecture Decision Records** - Justification des choix
- **Performance Reports** - Benchmarks et optimisations
- **API Documentation** - Javadoc complet et exemples
- **Deployment Guides** - Instructions de déploiement

## 🔗 Navigation

### Projets Individuels
- [08 - Cache LRU](./08-lru-cache/) - Generics et collections avancées
- [09 - Analyseur CSV](./09-csv-analyzer/) - Streams et programmation fonctionnelle
- [10 - Analyseur Logs](./10-log-analyzer/) - I/O moderne et NIO.2
- [11 - Backup Manager](./11-backup-manager/) - Concurrence et scheduling
- [12 - Chat Server](./12-chat-server/) - Modules et networking
- [13 - Application Modulaire](./13-modular-application/) - Architecture JPMS
- [14 - Mini Framework](./14-mini-framework/) - Design patterns avancés

### Autres Niveaux
- [🎮 Niveau 1: Console](../console-applications/) - Projets fondamentaux
- [🌐 Niveau 3: Spring Boot](../spring-boot-applications/) - Applications web
- [🏆 Vue d'ensemble](../README.md) - Tous les projets

### Documentation
- [🚀 Phase 2: Java Avancé](../../phase-2-advanced-java/) - Apprentissage théorique
- [📊 Tracker de Progression](../../docs/PROGRESS-TRACKER.md) - Suivi détaillé
- [🏠 Accueil Repository](../../README.md) - Vue globale

## ✅ Critères de Validation

### Fin de Niveau 2
Pour passer au Niveau 3, je maîtrise :

- [ ] **Generics complets** - Types, wildcards, PECS principle
- [ ] **Streams API** - Opérations intermédiaires/terminales, collectors
- [ ] **Lambdas & Method References** - Syntaxe et usage avancé
- [ ] **I/O moderne** - NIO.2, Files, Paths, performance
- [ ] **Concurrence** - Threads, ExecutorService, synchronisation
- [ ] **Modules** - JPMS, exports, requires, services
- [ ] **Architecture** - Design patterns, clean code

### Métriques de Réussite
- [ ] **7 projets complétés** - Tous optimisés et documentés
- [ ] **4,900+ lignes de code** - Code Java avancé
- [ ] **355+ tests** - Incluant tests de performance
- [ ] **Score examens blancs** - 70%+ régulièrement
- [ ] **Architecture propre** - Modules et patterns

**🎯 Prêt pour le Niveau 3 !** → [Spring Boot Applications](../spring-boot-applications/)

---

*Niveau 2 créé le [Date] - Progression : 0/7 projets*
