# 🏆 Projets Phares - Java OCP Certification Journey

[![Projects](https://img.shields.io/badge/Projects-0/19-red?style=for-the-badge)](../README.md)
[![Levels](https://img.shields.io/badge/Levels-3-blue?style=for-the-badge)](#niveaux-de-projets)
[![Progress](https://img.shields.io/badge/Progress-0%25-red?style=for-the-badge)](../docs/PROGRESS-TRACKER.md)

## 🎯 Vue d'Ensemble

Cette section regroupe les **19 projets phares** du parcours de certification OCP Java SE 17, organisés en 3 niveaux progressifs. Chaque projet est conçu pour maîtriser des concepts spécifiques tout en construisant un portfolio GitHub professionnel.

### Philosophie des Projets
- **Progression graduelle** - Du console aux microservices
- **Concepts ciblés** - Chaque projet maîtrise des compétences précises
- **Qualité professionnelle** - Code, tests, documentation exemplaires
- **Portfolio employable** - Projets attractifs pour recruteurs

## 📊 Niveaux de Projets

### 🎮 Niveau 1 : Applications Console (7 projets)
**Objectif :** Maîtriser les fondamentaux Java
**Durée :** Phase 1 (Semaines 1-16)
**Technologies :** Java SE 17, JUnit 5, Maven

| # | Projet | Statut | Concepts Clés |
|---|---------|---------|---------------|
| 1 | [Calculatrice](./console-applications/01-calculator/) | ⏳ | Syntaxe, Git workflow |
| 2 | [Jeu Devinette](./console-applications/02-guessing-game/) | ⏳ | TDD, debugging |
| 3 | [Système Bancaire](./console-applications/03-banking-system/) | ⏳ | POO, encapsulation |
| 4 | [Zoo Virtuel](./console-applications/04-virtual-zoo/) | ⏳ | Héritage, polymorphisme |
| 5 | [Carnet d'Adresses](./console-applications/05-address-book/) | ⏳ | Collections, recherche |
| 6 | [Gestionnaire de Tâches](./console-applications/06-task-manager/) | ⏳ | I/O, exceptions |
| 7 | [Système Bibliothèque](./console-applications/07-library-system/) | ⏳ | Architecture complète |

### ⚡ Niveau 2 : Projets Core Java (7 projets)
**Objectif :** Maîtriser Java SE 17 avancé
**Durée :** Phase 2 (Semaines 17-32)
**Technologies :** Generics, Streams, Concurrence, Modules

| # | Projet | Statut | Concepts Clés |
|---|---------|---------|---------------|
| 8 | [Cache LRU](./core-java-projects/08-lru-cache/) | ⏳ | Generics, Collections |
| 9 | [Analyseur CSV](./core-java-projects/09-csv-analyzer/) | ⏳ | Streams, Lambdas |
| 10 | [Analyseur Logs](./core-java-projects/10-log-analyzer/) | ⏳ | I/O, NIO.2 |
| 11 | [Backup Manager](./core-java-projects/11-backup-manager/) | ⏳ | Concurrence, I/O |
| 12 | [Chat Server](./core-java-projects/12-chat-server/) | ⏳ | Modules, Networking |
| 13 | [Application Modulaire](./core-java-projects/13-modular-application/) | ⏳ | JPMS, Architecture |
| 14 | [Mini Framework](./core-java-projects/14-mini-framework/) | ⏳ | Design Patterns |

### 🌐 Niveau 3 : Applications Spring Boot (5 projets)
**Objectif :** Développer des applications web modernes
**Durée :** Phase 4 (Semaines 33-50)
**Technologies :** Spring Boot, REST APIs, Microservices

| # | Projet | Statut | Concepts Clés |
|---|---------|---------|---------------|
| 15 | [REST API Simple](./spring-boot-applications/15-rest-api-simple/) | ⏳ | Spring Boot, Web |
| 16 | [Employee Management](./spring-boot-applications/16-employee-management/) | ⏳ | Spring Data, JPA |
| 17 | [E-commerce Backend](./spring-boot-applications/17-ecommerce-backend/) | ⏳ | Security, Tests |
| 18 | [Portfolio Website](./spring-boot-applications/18-portfolio-website/) | ⏳ | Web, Deployment |
| 19 | [Microservices Demo](./spring-boot-applications/19-microservices-demo/) | ⏳ | Microservices |

## 📈 Progression Globale

### Métriques Actuelles
- **Projets complétés :** 0 / 19
- **Lignes de code :** 0 / ~12,700 lignes prévues
- **Tests écrits :** 0 / ~963 tests prévus
- **Technologies maîtrisées :** 0 / 15 technologies

### Répartition par Niveau
```
Niveau 1 (Console):     ████████████████████████████████████████ 0/7
Niveau 2 (Core Java):   ████████████████████████████████████████ 0/7  
Niveau 3 (Spring Boot): ████████████████████████████████████████ 0/5
```

### Compétences Développées
- **Syntaxe Java :** ⭐⭐⭐⭐⭐ (0/5)
- **POO :** ⭐⭐⭐⭐⭐ (0/5)
- **Collections :** ⭐⭐⭐⭐⭐ (0/5)
- **Streams/Lambdas :** ⭐⭐⭐⭐⭐ (0/5)
- **Concurrence :** ⭐⭐⭐⭐⭐ (0/5)
- **Spring Boot :** ⭐⭐⭐⭐⭐ (0/5)

## 🛠️ Standards de Qualité

### Code
- **Conventions Java** - Respect des standards Oracle
- **Clean Code** - Lisibilité, maintenabilité
- **Design Patterns** - Application appropriée
- **Performance** - Optimisation et bonnes pratiques

### Tests
- **Couverture :** 85%+ minimum
- **Types :** Unitaires, intégration, end-to-end
- **TDD :** Test-Driven Development appliqué
- **Mocking :** Utilisation appropriée des mocks

### Documentation
- **README complet** - Installation, usage, architecture
- **Javadoc** - Documentation API complète
- **Guides utilisateur** - Instructions claires
- **Spécifications techniques** - Architecture détaillée

### Git Workflow
- **Commits atomiques** - Messages descriptifs
- **Branches features** - Développement organisé
- **Releases** - Versioning sémantique
- **Issues/PRs** - Gestion de projet

## 🔗 Navigation

### Par Niveau
- [🎮 Applications Console](./console-applications/) - 7 projets fondamentaux
- [⚡ Projets Core Java](./core-java-projects/) - 7 projets avancés
- [🌐 Applications Spring Boot](./spring-boot-applications/) - 5 projets modernes

### Par Phase d'Apprentissage
- [📚 Phase 1: Fondamentaux](../phase-1-fundamentals/) - Projets 1-7
- [🚀 Phase 2: Java Avancé](../phase-2-advanced-java/) - Projets 8-14
- [🌱 Phase 4: Spring Portfolio](../phase-4-spring-portfolio/) - Projets 15-19

### Documentation
- [📊 Tracker de Progression](../docs/PROGRESS-TRACKER.md)
- [📖 Plan d'Apprentissage](../docs/LEARNING-PLAN.md)
- [🏠 Accueil Repository](../README.md)

## 🎯 Objectifs par Niveau

### Niveau 1 : Fondations Solides
- **Syntaxe Java** - Maîtrise complète
- **POO** - 4 piliers + interfaces
- **Collections** - List, Set, Map
- **Tests** - JUnit 5, TDD
- **Git** - Workflow professionnel

### Niveau 2 : Expertise Technique
- **Generics** - Types, wildcards
- **Streams** - API complète
- **Concurrence** - Threads, synchronisation
- **Modules** - JPMS, encapsulation
- **Architecture** - Design patterns

### Niveau 3 : Employabilité
- **Spring Boot** - Ecosystem complet
- **REST APIs** - CRUD, sécurité
- **Persistance** - JPA, transactions
- **Déploiement** - Docker, CI/CD
- **Microservices** - Architecture moderne

## 📝 Prochaines Étapes

### Immédiat (Semaine 1)
1. **Commencer Projet 1** - Calculatrice Console
2. **Setup environnement** - JDK 17, IntelliJ, Git
3. **Premier commit** - Structure de base

### Court terme (Mois 1)
1. **Compléter Projets 1-2** - Calculatrice + Jeu Devinette
2. **Maîtriser TDD** - Cycle Red-Green-Refactor
3. **Git workflow** - Branches, commits, documentation

### Moyen terme (Trimestre 1)
1. **Terminer Niveau 1** - 7 projets console
2. **Portfolio initial** - GitHub attractif
3. **Préparation Niveau 2** - Concepts avancés

### Long terme (Année 1)
1. **Certification OCP** - Score 80%+
2. **Portfolio complet** - 19 projets
3. **Recherche emploi** - Développeur Java/Spring

---

*Portfolio créé le [Date] - Progression : 0/19 projets*
