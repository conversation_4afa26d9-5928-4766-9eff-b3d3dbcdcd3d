# 🌐 Applications Spring Boot - Niveau 3

[![Level](https://img.shields.io/badge/Level-3%20Spring%20Boot-purple?style=for-the-badge)](../README.md)
[![Projects](https://img.shields.io/badge/Projects-0/5-red?style=for-the-badge)](#projets-du-niveau)
[![Phase](https://img.shields.io/badge/Phase-4%20Portfolio-blue?style=for-the-badge)](../../phase-4-spring-portfolio/)

## 🎯 Objectifs du Niveau

Ce niveau développe l'**employabilité immédiate** avec des applications web modernes. Les 5 projets permettent de maîtriser :

- **Spring Boot Ecosystem** - IoC, DI, Auto-configuration, Starters
- **REST APIs complètes** - CRUD, validation, documentation, versioning
- **Persistance moderne** - Spring Data JPA, repositories, transactions
- **Sécurité web** - Spring Security, JWT, OAuth2, RBAC
- **Tests d'intégration** - TestContainers, MockMvc, WebTestClient
- **Déploiement moderne** - Docker, CI/CD, monitoring, observabilité
- **Architecture microservices** - Spring Cloud, service discovery, gateway

## 📋 Projets du Niveau

### Progression Actuelle (0/5)
| # | Projet | Statut | Concepts Clés | Lignes | Tests | Durée |
|---|---------|---------|---------------|--------|-------|-------|
| 15 | [REST API Simple](./15-rest-api-simple/) | ⏳ | Spring Boot, REST | ~600 | 45 | 3 sem |
| 16 | [Employee Management](./16-employee-management/) | ⏳ | Spring Data, JPA | ~800 | 60 | 3 sem |
| 17 | [E-commerce Backend](./17-ecommerce-backend/) | ⏳ | Security, Tests | ~1200 | 90 | 3 sem |
| 18 | [Portfolio Website](./18-portfolio-website/) | ⏳ | Web, Deployment | ~1000 | 75 | 3 sem |
| 19 | [Microservices Demo](./19-microservices-demo/) | ⏳ | Microservices | ~1500 | 120 | 6 sem |

### Métriques Globales
- **Total lignes de code :** 0 / 5,100 lignes Java
- **Total tests :** 0 / 390 tests (unitaires + intégration)
- **APIs créées :** 0 / 25+ endpoints REST
- **Services déployés :** 0 / 8 services

## 🚀 Parcours d'Apprentissage

### Projet 15 : REST API Simple
**Semaines 33-35 | Concepts : Spring Boot, REST, Validation**

Première API REST avec Spring Boot, validation et documentation.

**Fonctionnalités :**
- CRUD complet pour entité simple
- Validation des données avec Bean Validation
- Documentation API avec OpenAPI/Swagger
- Tests d'intégration avec TestContainers

**Apprentissages :**
- Spring Boot auto-configuration
- REST controllers et mappings
- Validation et gestion d'erreurs
- Documentation API automatique

**Stack Technique :**
- Spring Boot 3.x, Spring Web
- H2/PostgreSQL, Spring Data JPA
- Bean Validation, OpenAPI
- JUnit 5, TestContainers

### Projet 16 : Employee Management
**Semaines 36-38 | Concepts : Spring Data, JPA, Transactions**

Système de gestion d'employés avec relations complexes et transactions.

**Fonctionnalités :**
- Gestion employés, départements, projets
- Relations JPA complexes (OneToMany, ManyToMany)
- Transactions et gestion de la cohérence
- Recherche avancée avec Specifications

**Apprentissages :**
- Spring Data JPA repositories
- Relations et mappings JPA
- Transactions déclaratives
- Query methods et Specifications

**Stack Technique :**
- Spring Data JPA, Hibernate
- PostgreSQL, Flyway migrations
- Spring Transactions
- Criteria API, Specifications

### Projet 17 : E-commerce Backend
**Semaines 39-41 | Concepts : Security, Tests, Performance**

Backend e-commerce complet avec sécurité, paiements et tests avancés.

**Fonctionnalités :**
- Authentification JWT et OAuth2
- Gestion produits, commandes, paiements
- Autorisation basée sur les rôles (RBAC)
- Cache Redis et optimisations

**Apprentissages :**
- Spring Security configuration
- JWT et OAuth2 flows
- Caching avec Redis
- Tests de sécurité et performance

**Stack Technique :**
- Spring Security, JWT, OAuth2
- Redis, Spring Cache
- Stripe API, PayPal integration
- WireMock, Security tests

### Projet 18 : Portfolio Website
**Semaines 42-44 | Concepts : Web, Deployment, DevOps**

Site web portfolio avec interface moderne et déploiement automatisé.

**Fonctionnalités :**
- Interface web avec Thymeleaf/React
- CMS pour gestion du contenu
- Déploiement Docker et CI/CD
- Monitoring et observabilité

**Apprentissages :**
- Thymeleaf templating ou React integration
- Docker et containerisation
- CI/CD avec GitHub Actions
- Monitoring avec Actuator et Prometheus

**Stack Technique :**
- Thymeleaf ou React frontend
- Docker, Docker Compose
- GitHub Actions, Heroku/AWS
- Actuator, Prometheus, Grafana

### Projet 19 : Microservices Demo
**Semaines 45-50 | Concepts : Microservices, Spring Cloud**

Architecture microservices complète avec Spring Cloud.

**Fonctionnalités :**
- 4-5 microservices découplés
- Service discovery et load balancing
- API Gateway et routing
- Configuration centralisée et monitoring

**Apprentissages :**
- Architecture microservices
- Spring Cloud ecosystem
- Service mesh et communication
- Observabilité distribuée

**Stack Technique :**
- Spring Cloud Gateway, Eureka
- Config Server, Sleuth, Zipkin
- Docker Compose, Kubernetes
- ELK Stack, distributed tracing

## 📊 Standards de Qualité

### Architecture Web
- **REST API Design** - Conventions RESTful, versioning
- **Security** - Authentication, authorization, OWASP
- **Performance** - Caching, optimisation, monitoring
- **Scalabilité** - Stateless, horizontal scaling

### Tests Avancés
- **Couverture :** 85%+ avec tests d'intégration
- **TestContainers :** Tests avec vraies bases de données
- **Security Testing :** Tests d'authentification et autorisation
- **Performance Testing :** Load testing avec JMeter

### Déploiement
- **Containerisation :** Docker multi-stage builds
- **CI/CD :** Pipeline automatisé complet
- **Monitoring :** Métriques, logs, alertes
- **Documentation :** API docs, deployment guides

### Code Production
- **Configuration** - Externalisée et par environnement
- **Logging** - Structured logging avec correlation IDs
- **Error Handling** - Global exception handling
- **Validation** - Input validation et sanitization

## 🔗 Navigation

### Projets Individuels
- [15 - REST API Simple](./15-rest-api-simple/) - Premier projet Spring Boot
- [16 - Employee Management](./16-employee-management/) - Spring Data et JPA
- [17 - E-commerce Backend](./17-ecommerce-backend/) - Sécurité et performance
- [18 - Portfolio Website](./18-portfolio-website/) - Web et déploiement
- [19 - Microservices Demo](./19-microservices-demo/) - Architecture microservices

### Autres Niveaux
- [🎮 Niveau 1: Console](../console-applications/) - Projets fondamentaux
- [⚡ Niveau 2: Core Java](../core-java-projects/) - Projets avancés
- [🏆 Vue d'ensemble](../README.md) - Tous les projets

### Documentation
- [🌱 Phase 4: Spring Portfolio](../../phase-4-spring-portfolio/) - Apprentissage théorique
- [📊 Tracker de Progression](../../docs/PROGRESS-TRACKER.md) - Suivi détaillé
- [🏠 Accueil Repository](../../README.md) - Vue globale

## ✅ Critères de Validation

### Fin de Niveau 3 - Employabilité
Pour être prêt à postuler, je maîtrise :

- [ ] **Spring Boot** - Ecosystem complet et auto-configuration
- [ ] **REST APIs** - Design, sécurité, documentation
- [ ] **Persistance** - JPA, repositories, transactions
- [ ] **Sécurité** - Authentication, authorization, best practices
- [ ] **Tests** - Unitaires, intégration, sécurité
- [ ] **Déploiement** - Docker, CI/CD, monitoring
- [ ] **Microservices** - Architecture distribuée

### Portfolio Employable
- [ ] **5 projets déployés** - URLs publiques fonctionnelles
- [ ] **Code source public** - GitHub avec documentation
- [ ] **APIs documentées** - Swagger/OpenAPI complet
- [ ] **Tests complets** - Couverture 85%+ démontrée
- [ ] **CI/CD fonctionnel** - Déploiement automatisé
- [ ] **Monitoring** - Métriques et observabilité

### Compétences Professionnelles
- [ ] **Architecture** - Conception d'applications web
- [ ] **Sécurité** - Implémentation sécurisée
- [ ] **Performance** - Optimisation et scalabilité
- [ ] **DevOps** - Déploiement et monitoring
- [ ] **Collaboration** - Git workflow, code review
- [ ] **Documentation** - Technique et utilisateur

**🎯 Prêt pour l'emploi !** → Recherche de poste développeur Java/Spring

---

*Niveau 3 créé le [Date] - Progression : 0/5 projets*
