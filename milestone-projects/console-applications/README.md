# 🎮 Applications Console - Niveau 1

[![Level](https://img.shields.io/badge/Level-1%20Console-green?style=for-the-badge)](../README.md)
[![Projects](https://img.shields.io/badge/Projects-0/7-red?style=for-the-badge)](#projets-du-niveau)
[![Phase](https://img.shields.io/badge/Phase-1%20Fundamentals-blue?style=for-the-badge)](../../phase-1-fundamentals/)

## 🎯 Objectifs du Niveau

Ce niveau constitue les **fondations solides** du parcours Java. Les 7 applications console permettent de maîtriser :

- **Syntaxe Java complète** - Variables, opérateurs, conditions, boucles
- **Programmation Orientée Objet** - Classes, objets, héritage, polymorphisme
- **Collections fondamentales** - ArrayList, HashMap, algorithmes de base
- **Gestion des erreurs** - Try-catch, exceptions personnalisées
- **Entrées/Sorties** - Lecture/écriture fichiers, persistance
- **Tests unitaires** - JUnit 5, TDD, couverture de code
- **Git workflow** - Commits, branches, documentation

## 📋 Projets du Niveau

### Progression Actuelle (0/7)
| # | Projet | Statut | Concepts Clés | Lignes | Tests | Durée |
|---|---------|---------|---------------|--------|-------|-------|
| 1 | [Calculatrice](./01-calculator/) | ⏳ | Syntaxe, Git workflow | ~150 | 15 | 2 sem |
| 2 | [Jeu Devinette](./02-guessing-game/) | ⏳ | TDD, debugging | ~200 | 20 | 2 sem |
| 3 | [Système Bancaire](./03-banking-system/) | ⏳ | POO, encapsulation | ~300 | 25 | 2 sem |
| 4 | [Zoo Virtuel](./04-virtual-zoo/) | ⏳ | Héritage, polymorphisme | ~400 | 30 | 2 sem |
| 5 | [Carnet d'Adresses](./05-address-book/) | ⏳ | Collections, recherche | ~350 | 28 | 3 sem |
| 6 | [Gestionnaire de Tâches](./06-task-manager/) | ⏳ | I/O, exceptions | ~500 | 40 | 3 sem |
| 7 | [Système Bibliothèque](./07-library-system/) | ⏳ | Architecture complète | ~800 | 60 | 2 sem |

### Métriques Globales
- **Total lignes de code :** 0 / 2,700 lignes Java
- **Total tests :** 0 / 218 tests unitaires
- **Couverture moyenne :** 0% / 85%+ objectif
- **Durée totale :** 0 / 16 semaines

## 🚀 Parcours d'Apprentissage

### Projet 1 : Calculatrice Console
**Semaines 1-2 | Concepts : Syntaxe Java, Git workflow**

Premier contact avec Java et setup de l'environnement de développement professionnel.

**Fonctionnalités :**
- Interface console avec menu
- 4 opérations arithmétiques (+, -, *, /)
- Gestion des erreurs (division par zéro)
- Tests unitaires JUnit 5

**Apprentissages :**
- Installation JDK 17, IntelliJ IDEA, Git
- Structure d'un projet Maven
- Syntaxe Java de base
- Premier workflow Git

### Projet 2 : Jeu de Devinette
**Semaines 3-4 | Concepts : TDD, Debugging**

Introduction au Test-Driven Development et maîtrise des outils de debugging.

**Fonctionnalités :**
- Génération nombre aléatoire
- Système d'indices ("plus grand", "plus petit")
- Compteur de tentatives
- Niveaux de difficulté

**Apprentissages :**
- Cycle Red-Green-Refactor
- Debugging avec breakpoints
- Gestion des entrées utilisateur
- Refactoring sûr

### Projet 3 : Système Bancaire
**Semaines 5-6 | Concepts : POO, Encapsulation**

Première application orientée objet avec classes métier et validation.

**Fonctionnalités :**
- Comptes bancaires (courant, épargne)
- Opérations (dépôt, retrait, virement)
- Validation des montants
- Historique des transactions

**Apprentissages :**
- Classes et objets
- Encapsulation (getters/setters)
- Constructeurs et méthodes
- Validation métier

### Projet 4 : Zoo Virtuel
**Semaines 7-8 | Concepts : Héritage, Polymorphisme**

Hiérarchie d'objets complexe démontrant l'héritage et le polymorphisme.

**Fonctionnalités :**
- Hiérarchie d'animaux (mammifères, oiseaux, reptiles)
- Comportements spécialisés par espèce
- Système de nourrissage
- Statistiques du zoo

**Apprentissages :**
- Héritage et super
- Polymorphisme et méthodes virtuelles
- Classes abstraites et interfaces
- Design orienté objet

### Projet 5 : Carnet d'Adresses
**Semaines 9-11 | Concepts : Collections, Recherche**

Gestion de données avec les collections Java et algorithmes de recherche.

**Fonctionnalités :**
- Gestion contacts (CRUD)
- Recherche multi-critères
- Tri par différents champs
- Import/export CSV

**Apprentissages :**
- ArrayList et HashMap
- Algorithmes de tri et recherche
- Comparator et Comparable
- Manipulation de chaînes

### Projet 6 : Gestionnaire de Tâches
**Semaines 12-14 | Concepts : I/O, Exceptions**

Application avec persistance fichier et gestion robuste des erreurs.

**Fonctionnalités :**
- Gestion de tâches (TODO list)
- Priorités et échéances
- Sauvegarde automatique
- Gestion des erreurs

**Apprentissages :**
- Lecture/écriture fichiers
- Try-catch-finally
- Exceptions personnalisées
- Persistance de données

### Projet 7 : Système Bibliothèque
**Semaines 15-16 | Concepts : Architecture complète**

Projet de synthèse intégrant tous les concepts appris avec architecture propre.

**Fonctionnalités :**
- Gestion livres et membres
- Système d'emprunt/retour
- Recherche avancée
- Rapports et statistiques

**Apprentissages :**
- Architecture en couches
- Séparation des responsabilités
- Tests d'intégration
- Documentation complète

## 📊 Standards de Qualité

### Code
- **Conventions Java** - Nommage, indentation, structure
- **Clean Code** - Méthodes courtes, noms explicites
- **Commentaires** - Javadoc sur méthodes publiques
- **Gestion erreurs** - Try-catch approprié

### Tests
- **Couverture :** 85%+ minimum par projet
- **TDD :** Appliqué dès le projet 2
- **Assertions :** AssertJ pour la lisibilité
- **Organisation :** Given-When-Then

### Documentation
- **README :** Installation, usage, architecture
- **Javadoc :** API documentation
- **User Guide :** Guide utilisateur
- **Technical Specs :** Spécifications techniques

### Git
- **Commits :** Messages descriptifs, atomiques
- **Branches :** Feature branches pour développement
- **Releases :** Tags pour versions stables
- **Issues :** Gestion des tâches et bugs

## 🔗 Navigation

### Projets Individuels
- [01 - Calculatrice](./01-calculator/) - Premier projet Java
- [02 - Jeu Devinette](./02-guessing-game/) - TDD et debugging
- [03 - Système Bancaire](./03-banking-system/) - POO et encapsulation
- [04 - Zoo Virtuel](./04-virtual-zoo/) - Héritage et polymorphisme
- [05 - Carnet d'Adresses](./05-address-book/) - Collections et recherche
- [06 - Gestionnaire de Tâches](./06-task-manager/) - I/O et exceptions
- [07 - Système Bibliothèque](./07-library-system/) - Architecture complète

### Autres Niveaux
- [⚡ Niveau 2: Core Java](../core-java-projects/) - Projets avancés
- [🌐 Niveau 3: Spring Boot](../spring-boot-applications/) - Applications web
- [🏆 Vue d'ensemble](../README.md) - Tous les projets

### Documentation
- [📚 Phase 1: Fondamentaux](../../phase-1-fundamentals/) - Apprentissage théorique
- [📊 Tracker de Progression](../../docs/PROGRESS-TRACKER.md) - Suivi détaillé
- [🏠 Accueil Repository](../../README.md) - Vue globale

## ✅ Critères de Validation

### Fin de Niveau 1
Pour passer au Niveau 2, je maîtrise :

- [ ] **Syntaxe Java complète** - Variables, méthodes, classes
- [ ] **POO fondamentale** - 4 piliers + interfaces
- [ ] **Collections de base** - List, Set, Map usage
- [ ] **Gestion exceptions** - Try-catch + exceptions métier
- [ ] **I/O basique** - Lecture/écriture fichiers texte
- [ ] **Tests JUnit 5** - Assertions + configuration
- [ ] **Git workflow** - Branch, commit, merge, PR

### Métriques de Réussite
- [ ] **7 projets complétés** - Tous fonctionnels et testés
- [ ] **2,700+ lignes de code** - Code Java de qualité
- [ ] **218+ tests** - Couverture 85%+ moyenne
- [ ] **Documentation complète** - README et guides
- [ ] **Git professionnel** - Commits et branches propres

**🎯 Prêt pour le Niveau 2 !** → [Core Java Projects](../core-java-projects/)

---

*Niveau 1 créé le [Date] - Progression : 0/7 projets*
