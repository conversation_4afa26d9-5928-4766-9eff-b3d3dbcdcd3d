# 🚀 Phase 2 : Java SE 17 Avancé (Semaines 17-32)

[![Phase Status](https://img.shields.io/badge/Status-Not%20Started-red?style=for-the-badge)](../docs/PROGRESS-TRACKER.md)
[![Projects](https://img.shields.io/badge/Projects-0/7-red?style=for-the-badge)](#projets-réalisés)
[![Duration](https://img.shields.io/badge/Duration-16%20weeks-blue?style=for-the-badge)](./README.md)

## 🎯 Objectifs de la Phase

À la fin de cette phase de 4 mois, je maîtrise :
- ✅ **Generics avancés** - Types paramétrés, wildcards, PECS principle
- ✅ **Streams & Lambdas** - API Streams complète, expressions lambda, method references
- ✅ **I/O & NIO.2** - Files, Paths, streams, manipulation fichiers moderne
- ✅ **Concurrence** - Threads, ExecutorService, synchronisation, atomic classes
- ✅ **Modules (JPMS)** - Java Platform Module System, encapsulation forte
- ✅ **Préparation OCP** - Score 70%+ aux examens blancs

## 📅 Planning Détaillé

| Période | Module | Focus Principal | Projet Livré | Statut |
|---------|--------|-----------------|---------------|---------|
| **S17-S19** | [Generics](./week-17-19-generics/) | Types génériques, wildcards | [Cache LRU](./week-17-19-generics/projects/08-lru-cache/) | ⏳ |
| **S20-S22** | [Streams & Lambdas](./week-20-22-streams-lambdas/) | API Streams, expressions lambda | [Analyseur CSV](./week-20-22-streams-lambdas/projects/09-csv-analyzer/) | ⏳ |
| **S23-S25** | [I/O & NIO.2](./week-23-25-io-nio2/) | Files, Paths, streams | [Analyseur Logs](./week-23-25-io-nio2/projects/10-log-analyzer/) | ⏳ |
| **S26-S28** | [Concurrency](./week-26-28-concurrency/) | Threads, ExecutorService | [Backup Manager](./week-26-28-concurrency/projects/11-backup-manager/) | ⏳ |
| **S29-S31** | [Modules](./week-29-31-modules/) | JPMS, modularité | [Chat Server](./week-29-31-modules/projects/12-chat-server/) | ⏳ |
| **S32** | [Synthèse](./week-32-synthesis/) | Révision, examen blanc | [Application Modulaire](./week-32-synthesis/projects/13-modular-application/) | ⏳ |

## 🏆 Projets Réalisés

### Applications Java SE Avancées (0/7)
| # | Projet | Concepts Clés | Lignes de Code | Tests | Démo |
|---|---------|---------------|----------------|--------|------|
| 8 | [Cache LRU](./week-17-19-generics/projects/08-lru-cache/) | Generics, Collections | ~400 | 35 tests | ⏳ |
| 9 | [Analyseur CSV](./week-20-22-streams-lambdas/projects/09-csv-analyzer/) | Streams, Lambdas | ~500 | 40 tests | ⏳ |
| 10 | [Analyseur Logs](./week-23-25-io-nio2/projects/10-log-analyzer/) | I/O, NIO.2, Streams | ~600 | 45 tests | ⏳ |
| 11 | [Backup Manager](./week-26-28-concurrency/projects/11-backup-manager/) | Concurrence, I/O | ~700 | 50 tests | ⏳ |
| 12 | [Chat Server](./week-29-31-modules/projects/12-chat-server/) | Modules, Concurrence | ~800 | 55 tests | ⏳ |
| 13 | [Application Modulaire](./week-32-synthesis/projects/13-modular-application/) | Architecture complète | ~900 | 60 tests | ⏳ |
| 14 | [Mini Framework](./week-32-synthesis/projects/14-mini-framework/) | Design patterns | ~1000 | 70 tests | ⏳ |

## 📊 Statistiques de la Phase

### Métriques de Code
- **Total lignes de code :** 0 / ~4,900 lignes Java
- **Tests écrits :** 0 / 355 tests unitaires
- **Couverture moyenne :** 0% / 85%+ objectif
- **Commits Git :** 0 / 200 commits prévus

### Compétences Acquises
- **Generics :** ⭐⭐⭐⭐⭐ (0/5)
- **Streams/Lambdas :** ⭐⭐⭐⭐⭐ (0/5)
- **I/O/NIO.2 :** ⭐⭐⭐⭐⭐ (0/5)
- **Concurrence :** ⭐⭐⭐⭐⭐ (0/5)
- **Modules :** ⭐⭐⭐⭐⭐ (0/5)
- **Architecture :** ⭐⭐⭐⭐⭐ (0/5)

### Préparation Certification
- **Examens blancs :** 0 / 8 examens prévus
- **Score moyen :** 0% / 70% objectif
- **Points faibles :** À identifier
- **Révisions :** 0h / 40h prévues

## 🔗 Navigation

### Modules de la Phase 2
- [Semaines 17-19: Generics](./week-17-19-generics/) - Types génériques et wildcards
- [Semaines 20-22: Streams & Lambdas](./week-20-22-streams-lambdas/) - Programmation fonctionnelle
- [Semaines 23-25: I/O & NIO.2](./week-23-25-io-nio2/) - Manipulation fichiers moderne
- [Semaines 26-28: Concurrency](./week-26-28-concurrency/) - Programmation concurrente
- [Semaines 29-31: Modules](./week-29-31-modules/) - Java Platform Module System
- [Semaine 32: Synthèse](./week-32-synthesis/) - Révision et examen blanc

### Autres Phases
- [📚 Phase 1: Fondamentaux](../phase-1-fundamentals/) - Syntaxe, POO, Collections
- [📋 Phase 3: Préparation OCP](../phase-3-ocp-preparation/) - Examen et certification
- [🌱 Phase 4: Spring Portfolio](../phase-4-spring-portfolio/) - Applications web modernes

## 📚 Ressources Utilisées

### Cours Principaux
- **Oracle MyLearn** - Java SE 17 Developer (modules avancés)
- **Pluralsight** - Java Advanced Topics
- **Udemy** - Java Streams & Lambdas Masterclass

### Livres de Référence
- **"Effective Java"** - Joshua Bloch (Generics, Concurrence)
- **"Java Concurrency in Practice"** - Brian Goetz
- **"Modern Java in Action"** - Raoul-Gabriel Urma

### Pratique & Exercices
- **LeetCode** - Medium/Hard problems
- **HackerRank** - Java Advanced Domain
- **Codewars** - Functional programming katas

## ✅ Prérequis pour Phase 3

Avant de passer à la Phase 3 (Préparation OCP), je maîtrise :

- [ ] **Generics complets** - Types, wildcards, PECS
- [ ] **Streams API** - Opérations intermédiaires/terminales
- [ ] **Lambdas & Method References** - Syntaxe et usage
- [ ] **I/O moderne** - NIO.2, Files, Paths
- [ ] **Concurrence** - Threads, ExecutorService, synchronisation
- [ ] **Modules** - JPMS, exports, requires
- [ ] **Score examens blancs** - 70%+ régulièrement

**🎯 Prêt pour la Phase 3 !** → [Préparation OCP](../phase-3-ocp-preparation/)

---

*Phase 2 à commencer après Phase 1 - Progression actuelle : 0%*
