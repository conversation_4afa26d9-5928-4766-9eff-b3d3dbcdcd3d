# 📅 Semaines 1-2 : Environment Setup & Git Workflow

[![Week Status](https://img.shields.io/badge/Status-Not%20Started-red?style=flat-square)](../../docs/PROGRESS-TRACKER.md)
[![Project](https://img.shields.io/badge/Project-Calculator%20Console-blue?style=flat-square)](./projects/01-calculator-console/)

## 🎯 Objectifs de la Période

**Focus principal :** Préparer l'environnement de développement parfait et maîtriser Git/GitHub pour un workflow professionnel dès le début.

### Compétences Visées
- [ ] **Installation environnement** - JDK 17, VS Code, Git
- [ ] **Configuration IDE** - Plugins, thème, raccourcis clavier
- [ ] **Maîtrise Git** - Init, add, commit, push, pull, branch
- [ ] **GitHub workflow** - Création repo, README, issues, releases
- [ ] **Premier programme** - Hello World → Calculatrice avec tests
- [ ] **Debugging IDE** - Breakpoints, step-by-step, variables watch

## 📚 Programme d'Apprentissage

### Semaine 1 : Installation & Configuration
#### Jour 1-2 : Environnement Java (4h)
- **Installation JDK 17** - Oracle JDK vs OpenJDK, PATH, JAVA_HOME
- **Setup VS Code** - Installation, Extension Pack for Java
- **Ressources :**
  - [Oracle JDK Download](https://www.oracle.com/java/technologies/downloads/)
  - [VS Code Java Setup Guide](https://code.visualstudio.com/docs/languages/java)

#### Jour 3-4 : Git & GitHub (4h)
- **Installation Git** - Configuration user.name, user.email
- **Concepts de base** - Repository, staging, commits, remote
- **Ressources :**
  - [Git Documentation](https://git-scm.com/doc)
  - [GitHub Getting Started](https://docs.github.com/en/get-started)

#### Jour 5-7 : Premier Code Java (4h)
- **Hello World** - Structure d'un programme, public static void main
- **Syntaxe de base** - Variables, types primitifs, opérateurs
- **Ressources :**
  - [FreeCodeCamp Java Course](https://www.youtube.com/watch?v=grEKMHGYyns) (0-2h)
  - [Oracle MyLearn Module 1](https://mylearn.oracle.com/ou/course/java-se-foundations/121733/)

### Semaine 2 : Développement & Tests
#### Jour 8-10 : Logique & Conditions (4h)
- **Instructions conditionnelles** - if/else, switch, opérateurs logiques
- **Boucles** - for, while, do-while
- **Ressources :**
  - Oracle MyLearn Module 2
  - CodingBat Logic-1

#### Jour 11-12 : Tests & Debugging (4h)
- **Introduction JUnit 5** - @Test, assertions, configuration
- **Debugging IDE** - Breakpoints, step over/into, variables
- **Ressources :**
  - [JUnit 5 Guide](https://junit.org/junit5/docs/current/user-guide/)
  - [VS Code Java Debugging](https://code.visualstudio.com/docs/java/java-debugging)

#### Jour 13-14 : Projet Final (4h)
- **Calculatrice Console** - Design, implémentation, tests
- **Git workflow** - Commits réguliers, branches, release

## 🏆 Projet : Calculatrice Console

### Description
Application console qui effectue les 4 opérations de base (+, -, *, /) avec gestion des erreurs et tests unitaires complets.

### Fonctionnalités
- [ ] **Interface utilisateur** - Menu console intuitif
- [ ] **4 opérations** - Addition, soustraction, multiplication, division
- [ ] **Gestion erreurs** - Division par zéro, saisies invalides
- [ ] **Tests JUnit** - Couverture 90%+ de toutes les méthodes
- [ ] **Git workflow** - Commits atomiques, branches features

### Structure du Code
```
01-calculator-console/
├── README.md                 # Documentation complète
├── pom.xml                   # Configuration Maven
├── src/
│   ├── main/java/com/calculator/
│   │   ├── Calculator.java   # Logique métier
│   │   ├── ConsoleUI.java    # Interface utilisateur
│   │   └── Main.java         # Point d'entrée
│   └── test/java/com/calculator/
│       ├── CalculatorTest.java # Tests unitaires
│       └── ConsoleUITest.java  # Tests UI
└── docs/
    ├── user-guide.md         # Guide utilisateur
    └── technical-specs.md    # Spécifications techniques
```

### Démonstration
```bash
=== CALCULATRICE CONSOLE ===
1. Addition
2. Soustraction
3. Multiplication
4. Division
5. Quitter

Votre choix: 1
Premier nombre: 15.5
Deuxième nombre: 7.2
Résultat: 15.5 + 7.2 = 22.7

Continuer? (o/n): n
Au revoir!
```

### Métriques Finales
- **Lignes de code :** 150 lignes Java
- **Tests :** 15 tests unitaires (couverture 92%)
- **Commits Git :** 8 commits bien documentés
- **Temps développement :** 8 heures

## ✅ Validation des Acquis

### Auto-évaluation (1-5 étoiles)
- **Installation environnement :** ⭐⭐⭐⭐⭐ (0/5)
- **Syntaxe Java de base :** ⭐⭐⭐⭐⭐ (0/5)
- **Git commands :** ⭐⭐⭐⭐⭐ (0/5)
- **GitHub workflow :** ⭐⭐⭐⭐⭐ (0/5)
- **Tests JUnit :** ⭐⭐⭐⭐⭐ (0/5)
- **Debugging IDE :** ⭐⭐⭐⭐⭐ (0/5)

### Quiz de Fin de Module
- [ ] **Question 1 :** Différence entre JDK, JRE et JVM ?
- [ ] **Question 2 :** Commandes Git essentielles ?
- [ ] **Question 3 :** Structure d'un test JUnit ?
- [ ] **Question 4 :** Comment débugger avec breakpoints ?
- [ ] **Question 5 :** Gestion division par zéro ?

**Score final :** 0/5 ⏳ **Prêt pour les Semaines 3-4 !**

## 📚 Ressources Utilisées

### Cours Vidéo
- [FreeCodeCamp Java Full Course](https://www.youtube.com/watch?v=grEKMHGYyns) (0-2h)
- [Derek Banas Java Tutorial](https://www.youtube.com/watch?v=TBWX97e1E9g) (syntaxe)

### Documentation Officielle
- [Oracle Java 17 Documentation](https://docs.oracle.com/en/java/javase/17/)
- [Git Documentation](https://git-scm.com/doc)
- [JUnit 5 User Guide](https://junit.org/junit5/docs/current/user-guide/)

### Exercices Pratiques
- [CodingBat Java](https://codingbat.com/java) - Warmup-1, Logic-1
- [W3Schools Java](https://www.w3schools.com/java/) - Syntaxe et exemples

## 🔗 Navigation

### Fichiers du Module
- [Setup Guide](./setup-guide/) - Instructions d'installation détaillées
- [Git Workflow](./git-workflow/) - Guide Git/GitHub complet
- [Projet Calculatrice](./projects/01-calculator-console/) - Code et documentation

### Progression
- [📊 Tracker de Progression](../../docs/PROGRESS-TRACKER.md)
- [📖 Plan d'Apprentissage](../../docs/LEARNING-PLAN.md)
- [🏠 Accueil Repository](../../README.md)

### Modules Suivants
- [Semaines 3-4: Debugging & TDD](../week-03-04-debugging/) - Prochaine étape
- [Vue d'ensemble Phase 1](../README.md) - Retour à la phase

---

*Module créé le [Date] - Statut : À commencer*
