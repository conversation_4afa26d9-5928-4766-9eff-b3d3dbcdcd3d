# 🚀 Guide de Démarrage Rapide - Semaine 1

[![Week](https://img.shields.io/badge/Week-1--2-green?style=for-the-badge)](./README.md)
[![VS Code](https://img.shields.io/badge/VS%20Code-Ready-blue?style=for-the-badge)](./setup-guide/vscode-java-setup.md)

## 🎯 Objectif de la Semaine

**Mettre en place un environnement de développement Java professionnel avec VS Code et créer votre premier projet.**

## ⏱️ Planning Recommandé (6h total)

### 📅 **Jour 1 (<PERSON>i) - 2h**
**Setup Environnement**

#### 1. Installation Java 17 (30 min)
```bash
# Vérifier si Java est déjà installé
java -version

# Si pas Java 17, télécharger depuis :
# https://www.oracle.com/java/technologies/downloads/
# ou utiliser SDKMAN (recommandé) :
curl -s "https://get.sdkman.io" | bash
sdk install java 17.0.9-oracle
```

#### 2. Installation VS Code (15 min)
- Télécharger depuis [code.visualstudio.com](https://code.visualstudio.com/)
- Installer Extension Pack for Java

#### 3. Configuration Git (30 min)
```bash
# Configuration globale
git config --global user.name "Votre Nom"
git config --global user.email "<EMAIL>"

# Créer repository GitHub
# 1. Aller sur github.com
# 2. Créer nouveau repository "java-ocp-certification-journey"
# 3. Cloner localement
git clone https://github.com/votre-username/java-ocp-certification-journey.git
```

#### 4. Test Configuration (45 min)
- Suivre le [Guide Setup VS Code](./setup-guide/vscode-java-setup.md)
- Créer projet test et valider que tout fonctionne

### 📅 **Jour 2 (Mercredi) - 2h**
**Premier Projet Java**

#### 1. Créer Projet Calculatrice (90 min)
```bash
# Aller dans le dossier du projet
cd java-ocp-certification-journey/phase-1-fundamentals/week-01-02-environment/projects/01-calculator-console

# Copier le template Maven
cp -r ../../../../templates/maven-java-project/* .

# Ouvrir dans VS Code
code .
```

#### 2. Premier Code (30 min)
- Modifier `src/main/java/com/ocpjourney/Main.java`
- Créer classe `Calculator` basique
- Tester compilation et exécution

### 📅 **Jour 3 (Samedi) - 2h**
**Tests et Git Workflow**

#### 1. Écrire Tests JUnit (60 min)
- Créer tests pour la classe Calculator
- Exécuter tests dans VS Code
- Vérifier couverture de code

#### 2. Git Workflow (60 min)
- Commits réguliers avec messages clairs
- Créer première release
- Mettre à jour documentation

## 🛠️ Checklist de Validation

### ✅ **Setup Technique**
- [ ] **Java 17** installé et configuré (`java -version`)
- [ ] **VS Code** installé avec Extension Pack for Java
- [ ] **Git** configuré avec nom/email
- [ ] **Repository GitHub** créé et cloné
- [ ] **Template Maven** fonctionne (compilation + tests)

### ✅ **Premier Projet**
- [ ] **Projet Calculatrice** créé avec structure Maven
- [ ] **Classe Calculator** avec au moins 2 opérations
- [ ] **Tests JUnit** écrits et qui passent
- [ ] **Compilation** sans erreurs
- [ ] **Exécution** du programme principal

### ✅ **Git et Documentation**
- [ ] **Commits réguliers** avec messages descriptifs
- [ ] **README** du projet mis à jour
- [ ] **Progress tracker** mis à jour
- [ ] **Rapport hebdomadaire** créé

## 🎯 Résultat Attendu

À la fin de la semaine, vous devriez avoir :

### 📁 **Structure de Projet**
```
java-ocp-certification-journey/
├── phase-1-fundamentals/
│   └── week-01-02-environment/
│       ├── projects/
│       │   └── 01-calculator-console/
│       │       ├── src/main/java/com/calculator/
│       │       │   ├── Main.java
│       │       │   └── Calculator.java
│       │       ├── src/test/java/com/calculator/
│       │       │   └── CalculatorTest.java
│       │       ├── pom.xml
│       │       └── README.md
│       └── weekly-report-week-01.md
├── docs/
│   └── PROGRESS-TRACKER.md (mis à jour)
└── README.md (mis à jour)
```

### 💻 **Code Fonctionnel**
```java
// Exemple de ce que vous devriez avoir
public class Calculator {
    public double add(double a, double b) {
        return a + b;
    }
    
    public double subtract(double a, double b) {
        return a - b;
    }
    
    // + 2 autres opérations
}

// Tests correspondants
@Test
void testAdd() {
    Calculator calc = new Calculator();
    assertEquals(5.0, calc.add(2.0, 3.0));
}
```

### 📊 **Métriques**
- **Heures d'étude :** 6h
- **Commits Git :** 5-8 commits
- **Tests :** 4+ tests qui passent
- **Couverture :** 80%+ sur Calculator

## 🆘 **Aide et Dépannage**

### 🔧 **Problèmes Courants**

#### Java non détecté dans VS Code
```bash
# Vérifier configuration
Ctrl+Shift+P > "Java: Overview"

# Recharger projets
Ctrl+Shift+P > "Java: Reload Projects"
```

#### Maven ne compile pas
```bash
# Nettoyer et recompiler
mvn clean compile

# Vérifier version Maven
mvn -version
```

#### Tests ne s'exécutent pas
- Vérifier que JUnit 5 est dans le pom.xml
- Utiliser l'icône "Run Test" dans VS Code
- Ou terminal : `mvn test`

### 📚 **Ressources d'Aide**
- [Guide Setup VS Code Détaillé](./setup-guide/vscode-java-setup.md)
- [Documentation VS Code Java](https://code.visualstudio.com/docs/languages/java)
- [Maven Getting Started](https://maven.apache.org/guides/getting-started/)

## 🎉 **Prochaines Étapes**

Une fois cette semaine terminée :

1. **Semaine 2** - Continuer le projet Calculatrice
2. **Semaine 3-4** - Debugging et TDD avec Jeu de Devinette
3. **Mise à jour tracking** - Documenter votre progression

## 📝 **Template Rapport Hebdomadaire**

Créez `weekly-report-week-01.md` :

```markdown
# 📅 Rapport Semaine 1 - Environment Setup

## 🎯 Objectifs
- [x] Setup environnement Java 17 + VS Code
- [x] Configuration Git et GitHub
- [ ] Premier projet Calculatrice

## 💻 Travail Réalisé
### Lundi (2h)
- ✅ Installation Java 17
- ✅ Setup VS Code + extensions
- ✅ Configuration Git

### Mercredi (2h)
- ✅ Création projet Calculatrice
- ✅ Premier code Java

### Samedi (2h)
- 🔄 Tests JUnit (en cours)
- 🔄 Git workflow

## 📊 Métriques
- **Heures :** 4h / 6h
- **Commits :** 3 commits
- **Tests :** 2/4 tests

## 🎯 Semaine Prochaine
- Terminer tests Calculatrice
- Commencer debugging avancé
```

---

**🚀 Prêt à commencer votre parcours Java OCP !**
