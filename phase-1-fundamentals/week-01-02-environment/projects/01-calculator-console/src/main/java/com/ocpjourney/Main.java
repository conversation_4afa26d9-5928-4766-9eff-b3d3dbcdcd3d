package com.ocpjourney;

/**
 * Main class for the Java OCP project template.
 * 
 * This template provides a starting point for Java projects in the
 * OCP certification journey with proper Maven configuration,
 * testing setup, and project structure.
 * 
 * <AUTHOR> Student
 * @version 1.0.0
 * @since Java 17
 */
public class Main {
    
    /**
     * Main entry point of the application.
     * 
     * @param args command line arguments
     */
    public static void main(String[] args) {
        System.out.println("🚀 Java OCP Project Template");
        System.out.println("============================");
        System.out.println();
        
        // Display Java version information
        displayJavaInfo();
        
        // Run sample functionality
        runSampleCode();
        
        System.out.println();
        System.out.println("✅ Template execution completed successfully!");
    }
    
    /**
     * Displays Java runtime information.
     */
    private static void displayJavaInfo() {
        System.out.println("📋 Java Runtime Information:");
        System.out.println("   Java Version: " + System.getProperty("java.version"));
        System.out.println("   Java Vendor: " + System.getProperty("java.vendor"));
        System.out.println("   Java Home: " + System.getProperty("java.home"));
        System.out.println("   OS Name: " + System.getProperty("os.name"));
        System.out.println("   OS Version: " + System.getProperty("os.version"));
        System.out.println();
    }
    
    /**
     * Runs sample code to demonstrate basic functionality.
     */
    private static void runSampleCode() {
        System.out.println("🔧 Sample Code Execution:");
        
        // Sample: Working with variables and basic operations
        demonstrateBasicOperations();
        
        // Sample: Working with collections
        demonstrateCollections();
        
        // Sample: Working with modern Java features
        demonstrateModernJavaFeatures();
    }
    
    /**
     * Demonstrates basic Java operations.
     */
    private static void demonstrateBasicOperations() {
        System.out.println("   → Basic Operations:");
        
        int a = 10;
        int b = 20;
        int sum = a + b;
        
        System.out.println("     " + a + " + " + b + " = " + sum);
        
        String message = "Hello, Java 17!";
        System.out.println("     Message: " + message);
        System.out.println("     Message length: " + message.length());
    }
    
    /**
     * Demonstrates working with Java collections.
     */
    private static void demonstrateCollections() {
        System.out.println("   → Collections:");
        
        var numbers = java.util.List.of(1, 2, 3, 4, 5);
        System.out.println("     Numbers: " + numbers);
        
        var evenNumbers = numbers.stream()
                .filter(n -> n % 2 == 0)
                .toList();
        System.out.println("     Even numbers: " + evenNumbers);
    }
    
    /**
     * Demonstrates modern Java features (Java 17).
     */
    private static void demonstrateModernJavaFeatures() {
        System.out.println("   → Modern Java Features:");
        
        // Text blocks (Java 15+)
        var textBlock = """
                This is a text block
                that spans multiple lines
                and preserves formatting.
                """;
        System.out.println("     Text block preview: " + 
                textBlock.lines().findFirst().orElse(""));
        
        // Pattern matching for instanceof (Java 16+)
        Object obj = "Hello, Pattern Matching!";
        if (obj instanceof String str) {
            System.out.println("     Pattern matching result: " + str.toUpperCase());
        }
        
        // Records (Java 17)
        record Person(String name, int age) {}
        var person = new Person("Java Developer", 25);
        System.out.println("     Record example: " + person);
    }
}
