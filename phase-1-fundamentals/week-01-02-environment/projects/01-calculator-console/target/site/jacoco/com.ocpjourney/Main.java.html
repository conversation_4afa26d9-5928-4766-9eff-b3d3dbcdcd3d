<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="fr"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Main.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">Java OCP Project Template</a> &gt; <a href="index.source.html" class="el_package">com.ocpjourney</a> &gt; <span class="el_source">Main.java</span></div><h1>Main.java</h1><pre class="source lang-java linenums">package com.ocpjourney;

/**
 * Main class for the Java OCP project template.
 * 
 * This template provides a starting point for Java projects in the
 * OCP certification journey with proper Maven configuration,
 * testing setup, and project structure.
 * 
 * <AUTHOR> Student
 * @version 1.0.0
 * @since Java 17
 */
<span class="nc" id="L14">public class Main {</span>
    
    /**
     * Main entry point of the application.
     * 
     * @param args command line arguments
     */
    public static void main(String[] args) {
<span class="fc" id="L22">        System.out.println(&quot;🚀 Java OCP Project Template&quot;);</span>
<span class="fc" id="L23">        System.out.println(&quot;============================&quot;);</span>
<span class="fc" id="L24">        System.out.println();</span>
        
        // Display Java version information
<span class="fc" id="L27">        displayJavaInfo();</span>
        
        // Run sample functionality
<span class="fc" id="L30">        runSampleCode();</span>
        
<span class="fc" id="L32">        System.out.println();</span>
<span class="fc" id="L33">        System.out.println(&quot;✅ Template execution completed successfully!&quot;);</span>
<span class="fc" id="L34">    }</span>
    
    /**
     * Displays Java runtime information.
     */
    private static void displayJavaInfo() {
<span class="fc" id="L40">        System.out.println(&quot;📋 Java Runtime Information:&quot;);</span>
<span class="fc" id="L41">        System.out.println(&quot;   Java Version: &quot; + System.getProperty(&quot;java.version&quot;));</span>
<span class="fc" id="L42">        System.out.println(&quot;   Java Vendor: &quot; + System.getProperty(&quot;java.vendor&quot;));</span>
<span class="fc" id="L43">        System.out.println(&quot;   Java Home: &quot; + System.getProperty(&quot;java.home&quot;));</span>
<span class="fc" id="L44">        System.out.println(&quot;   OS Name: &quot; + System.getProperty(&quot;os.name&quot;));</span>
<span class="fc" id="L45">        System.out.println(&quot;   OS Version: &quot; + System.getProperty(&quot;os.version&quot;));</span>
<span class="fc" id="L46">        System.out.println();</span>
<span class="fc" id="L47">    }</span>
    
    /**
     * Runs sample code to demonstrate basic functionality.
     */
    private static void runSampleCode() {
<span class="fc" id="L53">        System.out.println(&quot;🔧 Sample Code Execution:&quot;);</span>
        
        // Sample: Working with variables and basic operations
<span class="fc" id="L56">        demonstrateBasicOperations();</span>
        
        // Sample: Working with collections
<span class="fc" id="L59">        demonstrateCollections();</span>
        
        // Sample: Working with modern Java features
<span class="fc" id="L62">        demonstrateModernJavaFeatures();</span>
<span class="fc" id="L63">    }</span>
    
    /**
     * Demonstrates basic Java operations.
     */
    private static void demonstrateBasicOperations() {
<span class="fc" id="L69">        System.out.println(&quot;   → Basic Operations:&quot;);</span>
        
<span class="fc" id="L71">        int a = 10;</span>
<span class="fc" id="L72">        int b = 20;</span>
<span class="fc" id="L73">        int sum = a + b;</span>
        
<span class="fc" id="L75">        System.out.println(&quot;     &quot; + a + &quot; + &quot; + b + &quot; = &quot; + sum);</span>
        
<span class="fc" id="L77">        String message = &quot;Hello, Java 17!&quot;;</span>
<span class="fc" id="L78">        System.out.println(&quot;     Message: &quot; + message);</span>
<span class="fc" id="L79">        System.out.println(&quot;     Message length: &quot; + message.length());</span>
<span class="fc" id="L80">    }</span>
    
    /**
     * Demonstrates working with Java collections.
     */
    private static void demonstrateCollections() {
<span class="fc" id="L86">        System.out.println(&quot;   → Collections:&quot;);</span>
        
<span class="fc" id="L88">        var numbers = java.util.List.of(1, 2, 3, 4, 5);</span>
<span class="fc" id="L89">        System.out.println(&quot;     Numbers: &quot; + numbers);</span>
        
<span class="fc" id="L91">        var evenNumbers = numbers.stream()</span>
<span class="fc bfc" id="L92" title="All 2 branches covered.">                .filter(n -&gt; n % 2 == 0)</span>
<span class="fc" id="L93">                .toList();</span>
<span class="fc" id="L94">        System.out.println(&quot;     Even numbers: &quot; + evenNumbers);</span>
<span class="fc" id="L95">    }</span>
    
    /**
     * Demonstrates modern Java features (Java 17).
     */
    private static void demonstrateModernJavaFeatures() {
<span class="fc" id="L101">        System.out.println(&quot;   → Modern Java Features:&quot;);</span>
        
        // Text blocks (Java 15+)
<span class="fc" id="L104">        var textBlock = &quot;&quot;&quot;</span>
                This is a text block
                that spans multiple lines
                and preserves formatting.
                &quot;&quot;&quot;;
<span class="fc" id="L109">        System.out.println(&quot;     Text block preview: &quot; + </span>
<span class="fc" id="L110">                textBlock.lines().findFirst().orElse(&quot;&quot;));</span>
        
        // Pattern matching for instanceof (Java 16+)
<span class="fc" id="L113">        Object obj = &quot;Hello, Pattern Matching!&quot;;</span>
<span class="pc bpc" id="L114" title="1 of 2 branches missed.">        if (obj instanceof String str) {</span>
<span class="fc" id="L115">            System.out.println(&quot;     Pattern matching result: &quot; + str.toUpperCase());</span>
        }
        
        // Records (Java 17)
<span class="fc" id="L119">        record Person(String name, int age) {}</span>
<span class="fc" id="L120">        var person = new Person(&quot;Java Developer&quot;, 25);</span>
<span class="fc" id="L121">        System.out.println(&quot;     Record example: &quot; + person);</span>
<span class="fc" id="L122">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>