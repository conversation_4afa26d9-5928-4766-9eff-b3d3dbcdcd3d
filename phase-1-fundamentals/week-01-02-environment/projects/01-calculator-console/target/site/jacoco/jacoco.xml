<?xml version="1.0" encoding="UTF-8" standalone="yes"?><!DOCTYPE report PUBLIC "-//JACOCO//DTD Report 1.1//EN" "report.dtd"><report name="Java OCP Project Template"><sessioninfo id="mac.home-bbf93922" start="1759263106496" dump="1759263107008"/><package name="com/ocpjourney"><class name="com/ocpjourney/Main$1Person" sourcefilename="Main.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;I)V" line="119"><counter type="INSTRUCTION" missed="0" covered="9"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="9"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/ocpjourney/Main" sourcefilename="Main.java"><method name="&lt;init&gt;" desc="()V" line="14"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="main" desc="([Ljava/lang/String;)V" line="22"><counter type="INSTRUCTION" missed="0" covered="16"/><counter type="LINE" missed="0" covered="8"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="displayJavaInfo" desc="()V" line="40"><counter type="INSTRUCTION" missed="0" covered="31"/><counter type="LINE" missed="0" covered="8"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="runSampleCode" desc="()V" line="53"><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="demonstrateBasicOperations" desc="()V" line="69"><counter type="INSTRUCTION" missed="0" covered="29"/><counter type="LINE" missed="0" covered="9"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="demonstrateCollections" desc="()V" line="86"><counter type="INSTRUCTION" missed="0" covered="32"/><counter type="LINE" missed="0" covered="8"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="demonstrateModernJavaFeatures" desc="()V" line="101"><counter type="INSTRUCTION" missed="0" covered="39"/><counter type="BRANCH" missed="1" covered="1"/><counter type="LINE" missed="0" covered="10"/><counter type="COMPLEXITY" missed="1" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$demonstrateCollections$0" desc="(Ljava/lang/Integer;)Z" line="92"><counter type="INSTRUCTION" missed="0" covered="9"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="3" covered="163"/><counter type="BRANCH" missed="1" covered="3"/><counter type="LINE" missed="1" covered="48"/><counter type="COMPLEXITY" missed="2" covered="8"/><counter type="METHOD" missed="1" covered="7"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="Main.java"><line nr="14" mi="3" ci="0" mb="0" cb="0"/><line nr="22" mi="0" ci="3" mb="0" cb="0"/><line nr="23" mi="0" ci="3" mb="0" cb="0"/><line nr="24" mi="0" ci="2" mb="0" cb="0"/><line nr="27" mi="0" ci="1" mb="0" cb="0"/><line nr="30" mi="0" ci="1" mb="0" cb="0"/><line nr="32" mi="0" ci="2" mb="0" cb="0"/><line nr="33" mi="0" ci="3" mb="0" cb="0"/><line nr="34" mi="0" ci="1" mb="0" cb="0"/><line nr="40" mi="0" ci="3" mb="0" cb="0"/><line nr="41" mi="0" ci="5" mb="0" cb="0"/><line nr="42" mi="0" ci="5" mb="0" cb="0"/><line nr="43" mi="0" ci="5" mb="0" cb="0"/><line nr="44" mi="0" ci="5" mb="0" cb="0"/><line nr="45" mi="0" ci="5" mb="0" cb="0"/><line nr="46" mi="0" ci="2" mb="0" cb="0"/><line nr="47" mi="0" ci="1" mb="0" cb="0"/><line nr="53" mi="0" ci="3" mb="0" cb="0"/><line nr="56" mi="0" ci="1" mb="0" cb="0"/><line nr="59" mi="0" ci="1" mb="0" cb="0"/><line nr="62" mi="0" ci="1" mb="0" cb="0"/><line nr="63" mi="0" ci="1" mb="0" cb="0"/><line nr="69" mi="0" ci="3" mb="0" cb="0"/><line nr="71" mi="0" ci="2" mb="0" cb="0"/><line nr="72" mi="0" ci="2" mb="0" cb="0"/><line nr="73" mi="0" ci="4" mb="0" cb="0"/><line nr="75" mi="0" ci="6" mb="0" cb="0"/><line nr="77" mi="0" ci="2" mb="0" cb="0"/><line nr="78" mi="0" ci="4" mb="0" cb="0"/><line nr="79" mi="0" ci="5" mb="0" cb="0"/><line nr="80" mi="0" ci="1" mb="0" cb="0"/><line nr="86" mi="0" ci="3" mb="0" cb="0"/><line nr="88" mi="0" ci="12" mb="0" cb="0"/><line nr="89" mi="0" ci="5" mb="0" cb="0"/><line nr="91" mi="0" ci="3" mb="0" cb="0"/><line nr="92" mi="0" ci="10" mb="0" cb="2"/><line nr="93" mi="0" ci="2" mb="0" cb="0"/><line nr="94" mi="0" ci="5" mb="0" cb="0"/><line nr="95" mi="0" ci="1" mb="0" cb="0"/><line nr="101" mi="0" ci="3" mb="0" cb="0"/><line nr="104" mi="0" ci="2" mb="0" cb="0"/><line nr="109" mi="0" ci="3" mb="0" cb="0"/><line nr="110" mi="0" ci="6" mb="0" cb="0"/><line nr="113" mi="0" ci="2" mb="0" cb="0"/><line nr="114" mi="0" ci="6" mb="1" cb="1"/><line nr="115" mi="0" ci="5" mb="0" cb="0"/><line nr="119" mi="0" ci="9" mb="0" cb="0"/><line nr="120" mi="0" ci="6" mb="0" cb="0"/><line nr="121" mi="0" ci="5" mb="0" cb="0"/><line nr="122" mi="0" ci="1" mb="0" cb="0"/><counter type="INSTRUCTION" missed="3" covered="172"/><counter type="BRANCH" missed="1" covered="3"/><counter type="LINE" missed="1" covered="49"/><counter type="COMPLEXITY" missed="2" covered="9"/><counter type="METHOD" missed="1" covered="8"/><counter type="CLASS" missed="0" covered="2"/></sourcefile><counter type="INSTRUCTION" missed="3" covered="172"/><counter type="BRANCH" missed="1" covered="3"/><counter type="LINE" missed="1" covered="49"/><counter type="COMPLEXITY" missed="2" covered="9"/><counter type="METHOD" missed="1" covered="8"/><counter type="CLASS" missed="0" covered="2"/></package><counter type="INSTRUCTION" missed="3" covered="172"/><counter type="BRANCH" missed="1" covered="3"/><counter type="LINE" missed="1" covered="49"/><counter type="COMPLEXITY" missed="2" covered="9"/><counter type="METHOD" missed="1" covered="8"/><counter type="CLASS" missed="0" covered="2"/></report>