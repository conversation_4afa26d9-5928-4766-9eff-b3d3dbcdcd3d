# 🏗️ Maven Java Project Template

[![Java](https://img.shields.io/badge/Java-17-orange?style=for-the-badge&logo=java)](https://openjdk.java.net/projects/jdk/17/)
[![Maven](https://img.shields.io/badge/Maven-3.9+-blue?style=for-the-badge&logo=apache-maven)](https://maven.apache.org/)
[![JUnit](https://img.shields.io/badge/JUnit-5-green?style=for-the-badge&logo=junit5)](https://junit.org/junit5/)

## 📋 Description

Template Maven standardisé pour les projets Java dans le cadre du parcours de certification OCP Java SE 17. Ce template inclut :

- **Configuration Maven complète** avec Java 17
- **Tests JUnit 5** avec AssertJ et Mockito
- **Couverture de code** avec JaCoCo
- **Structure de projet** standardisée
- **Exemples de code** Java 17 moderne

## 🚀 Utilisation Rapide

### 1. Copie<PERSON> le Template
```bash
# Copier le dossier template vers votre nouveau projet
cp -r templates/maven-java-project/ mon-nouveau-projet/
cd mon-nouveau-projet/
```

### 2. Personnaliser le Projet
```bash
# Modifier le pom.xml
# - Changer artifactId, name, description
# - Mettre à jour les informations développeur
# - Ajuster le package principal si nécessaire
```

### 3. Renommer les Packages
```bash
# Renommer le package com.ocpjourney vers votre package
# Exemple: com.ocpjourney -> com.monprojet
find src/ -name "*.java" -exec sed -i 's/com\.ocpjourney/com.monprojet/g' {} \;
```

### 4. Tester le Setup
```bash
# Compiler le projet
mvn clean compile

# Exécuter les tests
mvn test

# Générer le rapport de couverture
mvn test jacoco:report

# Créer le JAR exécutable
mvn package

# Exécuter l'application
java -jar target/project-template-1.0.0.jar
```

## 📁 Structure du Template

```
maven-java-project/
├── pom.xml                           # Configuration Maven
├── README.md                         # Ce fichier
├── .gitignore                        # Fichiers à ignorer
├── src/
│   ├── main/
│   │   └── java/
│   │       └── com/ocpjourney/
│   │           └── Main.java         # Classe principale
│   └── test/
│       └── java/
│           └── com/ocpjourney/
│               └── MainTest.java     # Tests unitaires
└── target/                           # Dossier de build (généré)
```

## 🔧 Configuration Maven

### Dépendances Incluses
- **JUnit 5** (5.10.0) - Framework de test moderne
- **Mockito** (5.5.0) - Framework de mocking
- **AssertJ** (3.24.2) - Assertions fluides

### Plugins Configurés
- **Maven Compiler Plugin** - Compilation Java 17
- **Maven Surefire Plugin** - Exécution des tests
- **JaCoCo Plugin** - Couverture de code (objectif 80%)
- **Maven JAR Plugin** - Création JAR exécutable

### Profils Disponibles
- **dev** (par défaut) - Développement avec tests
- **prod** - Production sans tests

## 🧪 Tests et Qualité

### Exécution des Tests
```bash
# Tous les tests
mvn test

# Tests spécifiques
mvn test -Dtest=MainTest

# Tests avec couverture
mvn test jacoco:report
```

### Rapport de Couverture
- **Localisation :** `target/site/jacoco/index.html`
- **Objectif :** 80% minimum
- **Échec build :** Si couverture < 80%

### Conventions de Test
- **Nommage :** `*Test.java` ou `*Tests.java`
- **Structure :** Given/When/Then
- **Assertions :** AssertJ pour la lisibilité
- **Mocking :** Mockito pour les dépendances

## 📚 Exemples de Code Inclus

### Fonctionnalités Java 17 Démontrées
- **Text Blocks** - Chaînes multi-lignes
- **Pattern Matching** - instanceof amélioré
- **Records** - Classes de données immutables
- **var keyword** - Inférence de type
- **Stream API** - Programmation fonctionnelle

### Structure de Test Complète
- **Tests unitaires** basiques
- **Tests paramétrés** avec @RepeatedTest
- **Tests imbriqués** avec @Nested
- **Capture de sortie** System.out
- **Tests de propriétés** système

## 🎯 Personnalisation

### 1. Informations du Projet
```xml
<!-- Dans pom.xml -->
<groupId>com.monentreprise</groupId>
<artifactId>mon-projet</artifactId>
<name>Mon Projet Java</name>
<description>Description de mon projet</description>
```

### 2. Package Principal
```java
// Renommer com.ocpjourney vers votre package
package com.monentreprise.monprojet;
```

### 3. Classe Principale
```xml
<!-- Dans pom.xml, plugin JAR -->
<mainClass>com.monentreprise.monprojet.Main</mainClass>
```

### 4. Dépendances Additionnelles
```xml
<!-- Ajouter dans <dependencies> -->
<dependency>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-lang3</artifactId>
    <version>3.12.0</version>
</dependency>
```

## 🔗 Commandes Utiles

### Développement
```bash
# Compilation rapide
mvn compile

# Nettoyage complet
mvn clean

# Compilation + tests + package
mvn clean package

# Exécution directe
mvn exec:java -Dexec.mainClass="com.ocpjourney.Main"
```

### Tests et Qualité
```bash
# Tests en mode watch (avec plugin)
mvn test -Dtest.watch=true

# Vérification couverture
mvn jacoco:check

# Rapport détaillé
mvn site
```

### Déploiement
```bash
# JAR avec dépendances
mvn clean package

# Installation locale
mvn install

# Déploiement (si configuré)
mvn deploy
```

## 📖 Bonnes Pratiques Incluses

### Code Java
- **Javadoc** sur toutes les méthodes publiques
- **Conventions de nommage** Java standard
- **Gestion des exceptions** appropriée
- **Utilisation moderne** des APIs Java 17

### Tests
- **Couverture élevée** (80%+ objectif)
- **Tests lisibles** avec noms descriptifs
- **Isolation** des tests unitaires
- **Assertions expressives** avec AssertJ

### Maven
- **Versions explicites** des dépendances
- **Plugins à jour** avec versions fixes
- **Profils** pour différents environnements
- **Propriétés centralisées** pour les versions

## 🚀 Prochaines Étapes

1. **Copier le template** vers votre nouveau projet
2. **Personnaliser** les informations du projet
3. **Renommer** les packages selon vos besoins
4. **Ajouter** vos dépendances spécifiques
5. **Développer** votre logique métier
6. **Tester** avec une couverture élevée
7. **Documenter** votre projet

---

**Template créé pour :** Java OCP Certification Journey
**Version :** 1.0.0
**Dernière mise à jour :** [Date]
