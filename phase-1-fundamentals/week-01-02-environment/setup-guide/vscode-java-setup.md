# 🛠️ Guide Setup VS Code pour Java

[![VS Code](https://img.shields.io/badge/VS%20Code-Java%20Ready-blue?style=for-the-badge&logo=visual-studio-code)](https://code.visualstudio.com/)
[![Java](https://img.shields.io/badge/Java-17-orange?style=for-the-badge&logo=java)](https://openjdk.java.net/projects/jdk/17/)

## 🎯 Objectif

Configurer VS Code comme IDE principal pour le développement Java avec toutes les fonctionnalités nécessaires pour le parcours OCP.

## 📋 Prérequis

- **Java 17** installé et configuré
- **VS Code** installé
- **Git** installé et configuré

## 🚀 Installation et Configuration

### 1. **Extension Pack for Java** (Essentiel)

#### Installation
```bash
# Méthode 1: Via l'interface VS Code
# 1. Ouvrir VS Code
# 2. Aller dans Extensions (Ctrl+Shift+X)
# 3. Chercher "Extension Pack for Java"
# 4. <PERSON><PERSON><PERSON> "Install"

# Méthode 2: Via la ligne de commande
code --install-extension vscjava.vscode-java-pack
```

#### Extensions Incluses
- **Language Support for Java** - Syntaxe, IntelliSense
- **Debugger for Java** - Debugging avancé
- **Test Runner for Java** - Exécution tests JUnit
- **Maven for Java** - Support Maven intégré
- **Project Manager for Java** - Gestion projets
- **Visual Studio IntelliCode** - AI-assisted coding

### 2. **Extensions Complémentaires Recommandées**

```bash
# Extensions utiles pour le parcours
code --install-extension redhat.vscode-xml          # Support XML (Maven)
code --install-extension ms-vscode.vscode-json     # Support JSON
code --install-extension eamodio.gitlens           # Git avancé
code --install-extension ms-vsliveshare.vsliveshare # Collaboration
code --install-extension sonarsource.sonarlint-vscode # Qualité code
```

### 3. **Configuration Java**

#### Vérification Java
```bash
# Vérifier Java installé
java -version
javac -version

# Localiser JAVA_HOME
echo $JAVA_HOME  # Linux/Mac
echo %JAVA_HOME% # Windows
```

#### Configuration VS Code
Ouvrir les settings (Ctrl+Shift+P > "Preferences: Open Settings JSON") :

```json
{
    // Configuration Java
    "java.home": "/path/to/your/jdk17",
    "java.configuration.runtimes": [
        {
            "name": "JavaSE-17",
            "path": "/path/to/your/jdk17",
            "default": true
        }
    ],
    
    // Compilation et analyse
    "java.compile.nullAnalysis.mode": "automatic",
    "java.completion.enabled": true,
    "java.completion.overwrite": true,
    
    // Formatage (Google Style)
    "java.format.settings.url": "https://raw.githubusercontent.com/google/styleguide/gh-pages/eclipse-java-google-style.xml",
    "java.format.enabled": true,
    
    // Tests
    "java.test.defaultConfig": "",
    "java.test.config": {},
    
    // Maven
    "maven.executable.path": "mvn",
    "maven.terminal.useJavaHome": true,
    
    // Debugging
    "java.debug.settings.enableRunDebugCodeLens": true,
    "java.debug.settings.showHex": false,
    "java.debug.settings.showStaticVariables": false,
    
    // Performance
    "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m"
}
```

## 🧪 Test de Configuration

### 1. **Créer un Projet Test**

```bash
# Créer un dossier test
mkdir vscode-java-test
cd vscode-java-test

# Ouvrir dans VS Code
code .
```

### 2. **Créer un Projet Maven**

Dans VS Code :
1. **Ctrl+Shift+P** > "Java: Create Java Project"
2. Choisir "Maven"
3. Choisir "maven-archetype-quickstart"
4. GroupId: `com.test`
5. ArtifactId: `vscode-test`

### 3. **Test Fonctionnalités**

#### Test 1: Compilation et Exécution
```java
// src/main/java/com/test/App.java
package com.test;

public class App {
    public static void main(String[] args) {
        System.out.println("🎉 VS Code Java Setup réussi !");
        
        // Test Java 17 features
        var message = """
                VS Code fonctionne parfaitement
                avec Java 17 !
                """;
        System.out.println(message);
    }
}
```

**Exécution :** Clic droit sur le fichier > "Run Java"

#### Test 2: Debugging
1. Placer un breakpoint (clic gauche dans la marge)
2. Clic droit > "Debug Java"
3. Vérifier que le debugger s'arrête au breakpoint

#### Test 3: Tests JUnit
```java
// src/test/java/com/test/AppTest.java
package com.test;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class AppTest {
    @Test
    void testApp() {
        assertTrue(true, "VS Code JUnit fonctionne !");
    }
}
```

**Exécution :** Clic sur l'icône "Run Test" au-dessus de la méthode

## 🎨 Personnalisation Recommandée

### 1. **Thème et Apparence**
```json
{
    "workbench.colorTheme": "Dark+ (default dark)",
    "editor.fontSize": 14,
    "editor.fontFamily": "'Fira Code', 'Courier New', monospace",
    "editor.fontLigatures": true,
    "editor.minimap.enabled": true,
    "editor.wordWrap": "on"
}
```

### 2. **Raccourcis Clavier Utiles**
- **Ctrl+Shift+P** - Palette de commandes
- **Ctrl+Shift+`** - Terminal intégré
- **F5** - Démarrer debugging
- **F9** - Toggle breakpoint
- **F10** - Step over
- **F11** - Step into
- **Ctrl+Shift+F** - Recherche globale
- **Ctrl+Shift+O** - Aller au symbole

### 3. **Snippets Java Personnalisés**
Créer `.vscode/java.code-snippets` :

```json
{
    "Main Method": {
        "prefix": "main",
        "body": [
            "public static void main(String[] args) {",
            "\t$0",
            "}"
        ],
        "description": "Main method"
    },
    "JUnit Test": {
        "prefix": "test",
        "body": [
            "@Test",
            "void ${1:testName}() {",
            "\t// Given",
            "\t$2",
            "\t",
            "\t// When",
            "\t$3",
            "\t",
            "\t// Then",
            "\t$0",
            "}"
        ],
        "description": "JUnit test method"
    }
}
```

## 🔧 Dépannage

### Problèmes Courants

#### 1. **Java non détecté**
```bash
# Vérifier la configuration
Ctrl+Shift+P > "Java: Overview"

# Recharger les projets
Ctrl+Shift+P > "Java: Reload Projects"
```

#### 2. **Maven ne fonctionne pas**
```bash
# Vérifier Maven
mvn -version

# Nettoyer et recompiler
mvn clean compile
```

#### 3. **Extensions ne se chargent pas**
```bash
# Redémarrer VS Code
Ctrl+Shift+P > "Developer: Reload Window"

# Réinstaller Extension Pack
code --uninstall-extension vscjava.vscode-java-pack
code --install-extension vscjava.vscode-java-pack
```

## ✅ Validation Setup

### Checklist de Validation
- [ ] **Java 17** détecté et configuré
- [ ] **Extension Pack for Java** installé et actif
- [ ] **Projet Maven** créé et compilé
- [ ] **Tests JUnit** exécutés avec succès
- [ ] **Debugging** fonctionnel avec breakpoints
- [ ] **IntelliSense** et auto-complétion actifs
- [ ] **Formatage automatique** configuré

### Test Final
Créer et exécuter le projet calculatrice du template pour valider que tout fonctionne parfaitement.

## 🔗 Ressources Utiles

### Documentation Officielle
- [VS Code Java Documentation](https://code.visualstudio.com/docs/languages/java)
- [Java Extension Pack](https://marketplace.visualstudio.com/items?itemName=vscjava.vscode-java-pack)
- [VS Code Java Debugging](https://code.visualstudio.com/docs/java/java-debugging)

### Tutoriels Vidéo
- [VS Code Java Getting Started](https://www.youtube.com/watch?v=BB0gZFpukJU)
- [Java Development in VS Code](https://www.youtube.com/watch?v=g8u-HLHJNtY)

---

**Setup créé le [Date] - VS Code prêt pour le parcours OCP !** 🚀
