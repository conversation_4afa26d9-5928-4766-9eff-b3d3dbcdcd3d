# 📅 Semaines 3-4 : Debugging & Test-Driven Development

[![Week Status](https://img.shields.io/badge/Status-Not%20Started-red?style=flat-square)](../../docs/PROGRESS-TRACKER.md)
[![Project](https://img.shields.io/badge/Project-Guessing%20Game%20TDD-blue?style=flat-square)](./projects/02-guessing-game-tdd/)

## 🎯 Objectifs de la Période

**Focus principal :** Maîtriser les techniques de debugging et adopter l'approche Test-Driven Development (TDD) pour développer du code robuste et maintenable.

### Compétences Visées
- [ ] **Debugging avancé** - Breakpoints, step-by-step, inspection variables
- [ ] **Test-Driven Development** - Red-Green-Refactor cycle
- [ ] **JUnit 5 avancé** - Assertions, parameterized tests, test lifecycle
- [ ] **Gestion des erreurs** - Exceptions, validation, edge cases
- [ ] **Refactoring** - Amélioration continue du code
- [ ] **Code coverage** - Mesure et amélioration de la couverture

## 📚 Programme d'Apprentissage

### Semaine 3 : Debugging Mastery
#### Jour 1-2 : Techniques de Debugging (4h)
- **Breakpoints avancés** - Conditional, exception, method breakpoints
- **Step debugging** - Step over, into, out, run to cursor
- **Variable inspection** - Watch, evaluate expression, memory view
- **Ressources :**
  - [VS Code Java Debugging Guide](https://code.visualstudio.com/docs/java/java-debugging)
  - [Java Debugging Best Practices](https://www.baeldung.com/java-debugging)

#### Jour 3-4 : Stack Traces & Logging (4h)
- **Reading stack traces** - Comprendre les erreurs Java
- **Logging basics** - System.out vs logging frameworks
- **Exception handling** - Try-catch, finally, custom exceptions
- **Ressources :**
  - [Oracle Exception Handling](https://docs.oracle.com/javase/tutorial/essential/exceptions/)
  - [Java Logging Best Practices](https://www.baeldung.com/java-logging-intro)

#### Jour 5-7 : Debugging Practice (4h)
- **Buggy code exercises** - Identifier et corriger des bugs
- **Performance debugging** - Profiling, memory leaks
- **IDE tools mastery** - Debugger features, shortcuts

### Semaine 4 : Test-Driven Development
#### Jour 8-10 : TDD Fundamentals (4h)
- **Red-Green-Refactor** - Cycle TDD complet
- **Test structure** - Given-When-Then, AAA pattern
- **JUnit 5 advanced** - Parameterized tests, nested tests
- **Ressources :**
  - [TDD by Example - Kent Beck](https://www.amazon.com/Test-Driven-Development-Kent-Beck/dp/0321146530)
  - [JUnit 5 User Guide](https://junit.org/junit5/docs/current/user-guide/)

#### Jour 11-12 : Advanced Testing (4h)
- **Test doubles** - Mocks, stubs, fakes
- **Edge cases** - Boundary testing, null handling
- **Code coverage** - JaCoCo, coverage analysis
- **Ressources :**
  - [Mockito Documentation](https://javadoc.io/doc/org.mockito/mockito-core/latest/org/mockito/Mockito.html)
  - [JaCoCo Documentation](https://www.jacoco.org/jacoco/trunk/doc/)

#### Jour 13-14 : TDD Project (4h)
- **Jeu de Devinette** - Développement complet en TDD
- **Refactoring** - Amélioration continue du design

## 🏆 Projet : Jeu de Devinette TDD

### Description
Jeu console où l'ordinateur génère un nombre aléatoire et le joueur doit le deviner. Développé entièrement en TDD pour démontrer la méthodologie.

### Fonctionnalités
- [ ] **Génération nombre aléatoire** - Dans une plage configurable
- [ ] **Interface de jeu** - Saisie et feedback utilisateur
- [ ] **Système d'indices** - "Plus grand", "Plus petit"
- [ ] **Compteur de tentatives** - Limitation et scoring
- [ ] **Niveaux de difficulté** - Facile, moyen, difficile
- [ ] **Statistiques** - Historique des parties

### Approche TDD
1. **Red** - Écrire un test qui échoue
2. **Green** - Écrire le minimum de code pour passer
3. **Refactor** - Améliorer le code sans casser les tests

### Structure du Code
```
02-guessing-game-tdd/
├── README.md
├── pom.xml
├── src/
│   ├── main/java/com/guessinggame/
│   │   ├── Game.java              # Logique principale
│   │   ├── NumberGenerator.java   # Génération aléatoire
│   │   ├── GameUI.java           # Interface utilisateur
│   │   ├── DifficultyLevel.java  # Enum niveaux
│   │   └── GameStatistics.java   # Statistiques
│   └── test/java/com/guessinggame/
│       ├── GameTest.java         # Tests TDD principaux
│       ├── NumberGeneratorTest.java
│       ├── GameUITest.java
│       └── GameStatisticsTest.java
└── docs/
    ├── tdd-log.md               # Journal TDD
    └── refactoring-notes.md     # Notes de refactoring
```

### Exemple de Session TDD
```java
// RED: Test qui échoue
@Test
void shouldGenerateNumberInRange() {
    NumberGenerator generator = new NumberGenerator(1, 10);
    int number = generator.generate();
    assertThat(number).isBetween(1, 10);
}

// GREEN: Code minimal
public class NumberGenerator {
    public int generate() {
        return 5; // Hard-coded pour passer le test
    }
}

// REFACTOR: Implémentation réelle
public class NumberGenerator {
    private final Random random = new Random();
    private final int min, max;

    public NumberGenerator(int min, int max) {
        this.min = min;
        this.max = max;
    }

    public int generate() {
        return random.nextInt(max - min + 1) + min;
    }
}
```

## ✅ Validation des Acquis

### Compétences de Debugging
- [ ] **Utiliser breakpoints** - Conditional, exception, method
- [ ] **Navigation debugging** - Step over/into/out efficacement
- [ ] **Inspection variables** - Watch, evaluate, memory view
- [ ] **Analyser stack traces** - Identifier rapidement les erreurs
- [ ] **Profiling basique** - Identifier les goulots d'étranglement

### Compétences TDD
- [ ] **Cycle Red-Green-Refactor** - Appliquer systématiquement
- [ ] **Tests first** - Écrire les tests avant le code
- [ ] **Refactoring sûr** - Améliorer sans casser
- [ ] **Coverage analysis** - Mesurer et améliorer la couverture
- [ ] **Edge cases** - Identifier et tester les cas limites

### Métriques du Projet
- **Tests écrits :** 0 / 20 tests prévus
- **Couverture :** 0% / 95% objectif
- **Cycles TDD :** 0 / 15 cycles prévus
- **Refactorings :** 0 / 5 refactorings prévus

## 📚 Ressources Utilisées

### Debugging
- [VS Code Java Debugging](https://code.visualstudio.com/docs/java/java-debugging)
- [Java Debugging Techniques](https://www.baeldung.com/java-debugging)
- [Effective Debugging - Diomidis Spinellis](https://www.spinellis.gr/debugging/)

### Test-Driven Development
- [TDD by Example - Kent Beck](https://www.amazon.com/Test-Driven-Development-Kent-Beck/dp/0321146530)
- [Growing Object-Oriented Software - Freeman & Pryce](https://www.amazon.com/Growing-Object-Oriented-Software-Guided-Tests/dp/0321503627)
- [JUnit 5 Documentation](https://junit.org/junit5/docs/current/user-guide/)

### Pratique
- [CodingBat Java](https://codingbat.com/java) - String-2, Logic-2
- [Debugging Exercises](https://github.com/debugging-exercises/java)

## 🔗 Navigation

### Fichiers du Module
- [Debugging Techniques](./debugging-techniques/) - Guides et exercices
- [Projet Jeu Devinette](./projects/02-guessing-game-tdd/) - Code et TDD log

### Progression
- [Module Précédent: Environment](../week-01-02-environment/) - Setup et premier projet
- [Module Suivant: POO Basics](../week-05-06-oop-basics/) - Classes et objets
- [Vue d'ensemble Phase 1](../README.md) - Retour à la phase

---

*Module créé le [Date] - Statut : À commencer*
