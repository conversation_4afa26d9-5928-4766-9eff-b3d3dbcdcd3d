# 📚 Phase 1 : Fondamentaux Java (Semaines 1-16)

[![Phase Status](https://img.shields.io/badge/Status-Not%20Started-red?style=for-the-badge)](../docs/PROGRESS-TRACKER.md)
[![Projects](https://img.shields.io/badge/Projects-0/7-red?style=for-the-badge)](#projets-réalisés)
[![Duration](https://img.shields.io/badge/Duration-16%20weeks-blue?style=for-the-badge)](./README.md)

## 🎯 Objectifs de la Phase

À la fin de cette phase de 4 mois, je maîtrise :
- ✅ **Syntaxe Java complète** - Variables, opérateurs, conditions, boucles
- ✅ **Programmation Orientée Objet** - Classes, objets, héritage, polymorphisme
- ✅ **Collections fondamentales** - ArrayList, HashMap, algorithmes de base
- ✅ **Gestion des erreurs** - Try-catch, exceptions personnalisées
- ✅ **Entrées/Sorties** - Lecture/écriture fichiers, persistance
- ✅ **Debugging avancé** - Breakpoints, inspection variables, stack traces
- ✅ **Tests unitaires** - JUnit 5, assertions, couverture de code
- ✅ **Git workflow** - Commits, branches, collaboration GitHub

## 📅 Planning Détaillé

| Période | Module | Focus Principal | Projet Livré | Statut |
|---------|--------|-----------------|---------------|---------|
| **S1-S2** | [Environment & Git](./week-01-02-environment/) | Installation, Git, premier code | [Calculatrice Console](./week-01-02-environment/projects/01-calculator-console/) | ⏳ |
| **S3-S4** | [Debugging & TDD](./week-03-04-debugging/) | Debugging, tests JUnit | [Jeu Devinette TDD](./week-03-04-debugging/projects/02-guessing-game-tdd/) | ⏳ |
| **S5-S6** | [POO Basics](./week-05-06-oop-basics/) | Classes, objets, encapsulation | [Système Bancaire](./week-05-06-oop-basics/projects/03-banking-system/) | ⏳ |
| **S7-S8** | [Inheritance](./week-07-08-inheritance/) | Héritage, polymorphisme | [Zoo Virtuel](./week-07-08-inheritance/projects/04-virtual-zoo/) | ⏳ |
| **S9-S11** | [Collections](./week-09-11-collections/) | ArrayList, HashMap, recherche | [Carnet d'Adresses](./week-09-11-collections/projects/05-address-book/) | ⏳ |
| **S12-S14** | [Exceptions & I/O](./week-12-14-exceptions-io/) | Try-catch, fichiers | [Gestionnaire Tâches](./week-12-14-exceptions-io/projects/06-task-manager/) | ⏳ |
| **S15-S16** | [Synthèse](./week-15-16-synthesis/) | Projet complet | [Système Bibliothèque](./week-15-16-synthesis/projects/07-library-system/) | ⏳ |

## 🏆 Projets Réalisés

### Applications Console (0/7)
| # | Projet | Concepts Clés | Lignes de Code | Tests | Démo |
|---|---------|---------------|----------------|--------|------|
| 1 | [Calculatrice](./week-01-02-environment/projects/01-calculator-console/) | Syntaxe, Git workflow | ~150 | 15 tests | ⏳ |
| 2 | [Jeu Devinette](./week-03-04-debugging/projects/02-guessing-game-tdd/) | TDD, debugging | ~200 | 20 tests | ⏳ |
| 3 | [Système Bancaire](./week-05-06-oop-basics/projects/03-banking-system/) | POO, encapsulation | ~300 | 25 tests | ⏳ |
| 4 | [Zoo Virtuel](./week-07-08-inheritance/projects/04-virtual-zoo/) | Héritage, polymorphisme | ~400 | 30 tests | ⏳ |
| 5 | [Carnet Adresses](./week-09-11-collections/projects/05-address-book/) | Collections, recherche | ~350 | 28 tests | ⏳ |
| 6 | [Gestionnaire Tâches](./week-12-14-exceptions-io/projects/06-task-manager/) | I/O, exceptions | ~500 | 40 tests | ⏳ |
| 7 | [Système Bibliothèque](./week-15-16-synthesis/projects/07-library-system/) | Architecture complète | ~800 | 60 tests | ⏳ |

## 📊 Statistiques de la Phase

### Métriques de Code
- **Total lignes de code :** 0 / ~2,700 lignes Java
- **Tests écrits :** 0 / 218 tests unitaires
- **Couverture moyenne :** 0% / 85%+ objectif
- **Commits Git :** 0 / 127 commits prévus

### Compétences Acquises
- **Syntaxe Java :** ⭐⭐⭐⭐⭐ (0/5)
- **POO :** ⭐⭐⭐⭐⭐ (0/5)
- **Collections :** ⭐⭐⭐⭐⭐ (0/5)
- **Debugging :** ⭐⭐⭐⭐⭐ (0/5)
- **Tests JUnit :** ⭐⭐⭐⭐⭐ (0/5)
- **Git/GitHub :** ⭐⭐⭐⭐⭐ (0/5)

### Temps d'Apprentissage
- **Heures totales :** 0h / 96h (6h × 16 semaines)
- **Répartition :** 0h étude + 0h projets
- **Rythme moyen :** 0h/semaine / 6h objectif

## 🔗 Navigation

### Modules de la Phase 1
- [Semaines 1-2: Environment & Git](./week-01-02-environment/) - Setup + Premier projet
- [Semaines 3-4: Debugging & TDD](./week-03-04-debugging/) - Debug + Tests
- [Semaines 5-6: POO Basics](./week-05-06-oop-basics/) - Classes et objets
- [Semaines 7-8: Inheritance](./week-07-08-inheritance/) - Héritage et polymorphisme
- [Semaines 9-11: Collections](./week-09-11-collections/) - ArrayList et HashMap
- [Semaines 12-14: Exceptions & I/O](./week-12-14-exceptions-io/) - Erreurs et fichiers
- [Semaines 15-16: Synthèse](./week-15-16-synthesis/) - Projet complet

### Autres Phases
- [🚀 Phase 2: Java Avancé](../phase-2-advanced-java/) - Generics, Streams, Concurrence
- [📋 Phase 3: Préparation OCP](../phase-3-ocp-preparation/) - Examen et certification
- [🌱 Phase 4: Spring Portfolio](../phase-4-spring-portfolio/) - Applications web modernes

## 📚 Ressources Utilisées

### Cours Principaux
- **Oracle MyLearn** - Java Foundations (modules 1-8)
- **FreeCodeCamp** - Java Full Course (14h de vidéo)
- **MOOC Helsinki** - Java Programming I (chapitres 1-8)

### Pratique & Exercices
- **CodingBat** - Java String-1, Logic-1, Array-1
- **HackerRank** - Java Domain (10 Days of Code)
- **LeetCode** - Easy problems (Arrays, Strings)

### Documentation
- **Oracle Java Documentation** - API officielle
- **Baeldung** - Tutorials POO, Collections, I/O
- **Java Brains** - Vidéos YouTube sur concepts clés

## ✅ Prérequis pour Phase 2

Avant de passer à la Phase 2 (Java Avancé), je maîtrise :

- [ ] **Syntaxe Java complète** - Variables à méthodes
- [ ] **POO fondamentale** - 4 piliers + interfaces
- [ ] **Collections de base** - List, Set, Map usage
- [ ] **Gestion exceptions** - Try-catch + exceptions métier
- [ ] **I/O basique** - Lecture/écriture fichiers texte
- [ ] **Tests JUnit 5** - Assertions + configuration
- [ ] **Git workflow** - Branch, commit, merge, PR

**🎯 Prêt pour la Phase 2 !** → [Java SE 17 Avancé](../phase-2-advanced-java/)

---

*Phase 1 commencée le [Date] - Progression actuelle : 0%*
