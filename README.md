# 🏆 Java OCP Certification Journey - Portfolio Professionnel

[![OCP Java SE 17](https://img.shields.io/badge/OCP-Java%20SE%2017-orange?style=for-the-badge&logo=oracle)](https://education.oracle.com/oracle-certified-professional-java-se-17-developer/trackp_OCPJSE17)
[![Progress](https://img.shields.io/badge/Progress-0%25-red?style=for-the-badge)](./docs/PROGRESS-TRACKER.md)
[![Projects](https://img.shields.io/badge/Projects-0/19-blue?style=for-the-badge)](#projets-réalisés)

> **Mission :** Obtenir la certification Oracle Certified Professional Java SE 17 Developer tout en construisant un portfolio GitHub professionnel et attractif.

## 👨‍💻 À Propos

Développeur en formation Java SE 17, je documente ici mon parcours de 12 mois vers la certification OCP et la maîtrise des technologies Java modernes. Ce repository contient tous mes projets d'apprentissage, du niveau console aux microservices Spring Boot.

**🎯 Objectifs :**
- [ ] Maîtriser Java SE 17 (syntaxe → concepts avancés)
- [ ] Obtenir la certification OCP Java SE 17 Developer (1Z0-829)
- [ ] Construire 19 projets progressifs et documentés
- [ ] Développer une expertise Spring Boot employable

## 🚀 Progression Actuelle

### Phase 1: Fondamentaux ⏳ **À commencer**
**Durée :** 16 semaines (Jan - Avr 2024)
**Focus :** Syntaxe Java, POO, Collections, Exceptions, I/O
**Projets :** 0/7 applications console

### Phase 2: Java SE 17 Avancé ⏳ **À venir**
**Durée :** 16 semaines (Mai - Août 2024)
**Focus :** Generics, Streams, Concurrence, Modules
**Projets :** 0/7 applications Java SE avancées

### Phase 3: Préparation OCP ⏳ **À venir**
**Durée :** 12 semaines (Sep - Nov 2024)
**Focus :** Révisions, examens blancs, certification
**Objectif :** Score 80%+ et certification obtenue

### Phase 4: Portfolio Spring Boot ⏳ **À venir**
**Durée :** 16 semaines (Sep - Déc 2024)
**Focus :** Spring Boot, REST APIs, Microservices
**Projets :** 0/5 applications web modernes

## 📊 Projets Réalisés

| Niveau | Projet | Statut | Technologies | Description |
|--------|---------|---------|--------------|-------------|
| **Console** | [Calculatrice](./milestone-projects/console-applications/01-calculator/) | ⏳ | Java, JUnit, Git | Premier projet avec TDD |
| **Console** | [Jeu Devinette](./milestone-projects/console-applications/02-guessing-game/) | ⏳ | Java, JUnit, Debugging | Approche TDD + debugging |
| **Console** | [Système Bancaire](./milestone-projects/console-applications/03-banking-system/) | ⏳ | Java, POO, Tests | Encapsulation et validation |
| **Console** | [Zoo Virtuel](./milestone-projects/console-applications/04-virtual-zoo/) | ⏳ | Java, Héritage, Polymorphisme | Hiérarchie d'objets complexe |
| **Console** | [Carnet d'Adresses](./milestone-projects/console-applications/05-address-book/) | ⏳ | Java, Collections, Recherche | ArrayList et HashMap |
| **Console** | [Gestionnaire de Tâches](./milestone-projects/console-applications/06-task-manager/) | ⏳ | Java, I/O, Exceptions | Persistance fichier + gestion erreurs |
| **Console** | [Système Bibliothèque](./milestone-projects/console-applications/07-library-system/) | ⏳ | Java, Architecture, Tests | Projet de synthèse Phase 1 |
| **Core Java** | [Cache LRU](./milestone-projects/core-java-projects/08-lru-cache/) | ⏳ | Generics, Collections | Implémentation cache générique |
| **Core Java** | [Analyseur CSV](./milestone-projects/core-java-projects/09-csv-analyzer/) | ⏳ | Generics, Streams | Processing données avec Streams |
| **Core Java** | [Analyseur Logs](./milestone-projects/core-java-projects/10-log-analyzer/) | ⏳ | Streams, Lambdas | Analyse logs avec API Streams |
| **Core Java** | Backup Manager | ⏳ | I/O, NIO.2 | - |
| **Core Java** | Chat Server | ⏳ | Concurrence, NIO | - |
| **Spring** | REST API Simple | ⏳ | Spring Boot, Web | - |

## 🛠️ Stack Technique

### Langages & Versions
- **Java SE 17** (LTS) - Niveau certification
- **Maven 3.9+** - Gestion des dépendances
- **Git 2.40+** - Contrôle de version

### Frameworks & Librairies
- **JUnit 5** + **Mockito** - Tests unitaires et mocks
- **Spring Boot 3.x** - Framework web moderne
- **Spring Data JPA** - Persistance base de données
- **H2 / PostgreSQL** - Bases de données

### Outils de Développement
- **VS Code** - IDE principal avec Extension Pack for Java
- **IntelliJ IDEA** - Alternative (optionnel)
- **GitHub Actions** - CI/CD (à venir)
- **Docker** - Containerisation (à venir)

## 📚 Documentation

- [📖 Plan d'Apprentissage Détaillé](./docs/LEARNING-PLAN.md)
- [📊 Tracker de Progression](./docs/PROGRESS-TRACKER.md)
- [🎓 Guide Certification OCP](./docs/CERTIFICATION-GUIDE.md)
- [📚 Ressources d'Apprentissage](./docs/RESOURCES.md)

## 🔗 Navigation Rapide

### Par Phase d'Apprentissage
- [Phase 1: Fondamentaux](./phase-1-fundamentals/) - Syntaxe, POO, Collections
- [Phase 2: Java Avancé](./phase-2-advanced-java/) - Streams, Concurrence, Modules
- [Phase 3: Préparation OCP](./phase-3-ocp-preparation/) - Révisions, Examens blancs
- [Phase 4: Spring Portfolio](./phase-4-spring-portfolio/) - Spring Boot, REST, Microservices

### Par Type de Projet
- [Applications Console](./milestone-projects/console-applications/) - 7 projets fondamentaux
- [Projets Core Java](./milestone-projects/core-java-projects/) - 7 projets avancés
- [Applications Spring Boot](./milestone-projects/spring-boot-applications/) - 5 projets modernes

## 📞 Contact & Réseaux

- **LinkedIn :** [Mon Profil](https://linkedin.com/in/monprofil)
- **Email :** <EMAIL>
- **Portfolio Web :** [monportfolio.dev](https://monportfolio.dev) *(à venir)*

---

⭐ **N'hésitez pas à explorer mes projets et à me faire des retours !**

*Dernière mise à jour : [Date] - Projet en cours : [Nom du projet]*
