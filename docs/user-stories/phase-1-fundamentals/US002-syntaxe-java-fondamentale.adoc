= US002 - Maîtriser Syntaxe Java Fondamentale
:status: Backlog
:priority: Must Have
:effort: 5 points
:epic: 🚀 Fondations Solides Java
:sprint: S01-S02 (Semaines 1-2)
:assigned: Étudiant Java OCP

[cols="1,2", options="header"]
|===
| Propriété | Valeur
| ID        | US002
| Titre     | Maîtriser la syntaxe Java fondamentale
| Epic      | {epic}
| Sprint    | {sprint}
| Priorité  | {priority}
| Effort    | {effort}
| Statut    | {status}
| Dépendances | US001 (Setup environnement)
|===

== 🎯 Description
En tant qu'étudiant Java débutant
Je veux maîtriser la syntaxe fondamentale de Java (variables, types, conditions, boucles, méthodes)
Afin de pouvoir écrire mes premiers programmes fonctionnels et comprendre la logique de programmation.

== ✅ Critères d'Acceptation

=== Types et Variables
* [ ] Types primitifs (int, double, boolean, char) maîtrisés
* [ ] Déclaration variables avec conventions de nommage
* [ ] Initialisation et valeurs par défaut comprises
* [ ] Constantes avec `final` et conventions UPPER_CASE

=== Structures de Contrôle
* [ ] Conditions if/else avec logique booléenne
* [ ] Switch statements avec cas multiples
* [ ] Boucles for avec compteurs et itération
* [ ] Boucles while/do-while sans boucles infinies

=== Méthodes et Classes
* [ ] Méthodes statiques (signature, paramètres, retour)
* [ ] Classes basiques (structure, attributs, constructeurs)
* [ ] Méthode main comme point d'entrée
* [ ] Visibilité (public, private, package-private)

=== Projet Pratique
* [ ] Calculatrice fonctionnelle (4 opérations arithmétiques)
* [ ] Interface console avec Scanner
* [ ] Gestion erreurs basique (division par zéro)
* [ ] Code organisé (séparation logique métier/interface)

== 🛠️ Tâches Détaillées

=== T006: Types Primitifs et Variables (1h)
[source,java]
----
// Types primitifs
int age = 25;
double prix = 19.99;
boolean estValide = true;
char grade = 'A';

// Constantes
final double PI = 3.14159;
final int MAX_USERS = 100;

// Conversions (casting)
int entier = (int) 3.14;  // 3
double decimal = 5;       // 5.0
----

Exercices pratiques :
1. Variables pour étudiant (nom, âge, note, actif)
2. Calcul moyenne de 3 notes
3. Conversion température Celsius → Fahrenheit

Validation :
* [ ] 10 exercices CodingBat Warmup-1 réussis
* [ ] Code utilisant tous les types primitifs
* [ ] Conversions de types maîtrisées

=== T007: Structures de Contrôle (1h30)
[source,java]
----
// Conditions
if (age >= 18) {
    System.out.println("Majeur");
} else {
    System.out.println("Mineur");
}

// Switch
switch (grade) {
    case 'A': System.out.println("Excellent"); break;
    case 'B': System.out.println("Bien"); break;
    default: System.out.println("À améliorer");
}

// Boucles
for (int i = 0; i < 10; i++) {
    System.out.println("Nombre: " + i);
}

int compteur = 0;
while (compteur < 5) {
    System.out.println("Compteur: " + compteur);
    compteur++;
}
----

Exercices pratiques :
1. Nombres pairs de 1 à 20
2. Calculateur de factorielle
3. Menu console avec switch
4. Validation d'entrée avec while

Validation :
* [ ] 15 exercices CodingBat Logic-1 réussis
* [ ] Tous types de boucles utilisés
* [ ] Conditions complexes avec opérateurs logiques

=== T008: Méthodes et Organisation (1h30)
[source,java]
----
public class Calculator {
    // Méthode avec paramètres et retour
    public static double add(double a, double b) {
        return a + b;
    }

    // Méthode sans retour (void)
    public static void afficherResultat(double resultat) {
        System.out.println("Résultat: " + resultat);
    }

    // Méthode avec validation
    public static double divide(double a, double b) {
        if (b == 0) {
            System.out.println("Erreur: Division par zéro!");
            return 0;
        }
        return a / b;
    }
}
----

Validation :
* [ ] 10 exercices CodingBat String-1 réussis
* [ ] Méthodes avec différents types de retour
* [ ] Paramètres et arguments maîtrisés

=== T009: Développement Calculatrice Console (2h)
Structure du projet :
----
src/main/java/com/calculator/
├── Main.java           // Point d'entrée et menu
├── Calculator.java     // Logique des calculs
├── ConsoleUI.java      // Interface utilisateur
└── InputValidator.java // Validation des entrées
----

[source,java]
----
public class Main {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        boolean continuer = true;

        while (continuer) {
            ConsoleUI.afficherMenu();
            int choix = InputValidator.lireChoix(scanner);

            switch (choix) {
                case 1: // Addition
                    double[] nombres = InputValidator.lireDeuxNombres(scanner);
                    double resultat = Calculator.add(nombres[0], nombres[1]);
                    ConsoleUI.afficherResultat(resultat);
                    break;
                case 5: // Quitter
                    continuer = false;
                    break;
            }
        }
    }
}
----

Validation :
* [ ] Application compile sans erreurs
* [ ] 4 opérations fonctionnelles
* [ ] Gestion division par zéro
* [ ] Interface utilisateur intuitive

== 📚 Ressources d'Apprentissage

=== 🎯 Ressources Principales (À étudier dans l'ordre)

==== **1. Cours Vidéo Complets**
* **FreeCodeCamp Java Full Course** ⭐⭐⭐⭐⭐
  - Lien : https://www.youtube.com/watch?v=grEKMHGYyns
  - Durée : 14h (regarder 0h-3h pour syntaxe de base)
  - Points forts : Gratuit, complet, exemples pratiques
* **Oracle Java Foundations** ⭐⭐⭐⭐⭐
  - Modules 1-3 : "Java Syntax and Class Review" (4h)
  - Lien : https://learn.oracle.com/ols/learning-path/oracle-java-foundations/88323/79726
  - Certification gratuite incluse

==== **2. Tutoriels Interactifs**
* **OpenClassrooms - Apprenez à programmer en Java** ⭐⭐⭐⭐⭐
  - Lien : https://openclassrooms.com/fr/courses/8383791-apprenez-a-programmer-en-java
  - Chapitres 1-4 : Variables, types, conditions, boucles
  - Exercices intégrés et quiz
* **Oracle Java Tutorial - Language Basics** ⭐⭐⭐⭐⭐
  - Lien : https://docs.oracle.com/javase/tutorial/java/nutsandbolts/
  - Documentation officielle avec exemples

==== **3. Pratique Intensive**
* **CodingBat Java** ⭐⭐⭐⭐⭐
  - Lien : https://codingbat.com/java
  - Sections : Warmup-1, Logic-1, String-1
  - Exercices courts et progressifs
* **HackerRank Java Domain** ⭐⭐⭐⭐
  - Lien : https://www.hackerrank.com/domains/java
  - Sections : Introduction, Strings, BigNumber

=== 📖 Ressources de Référence

==== **Documentation Officielle**
* **Java SE 17 API Documentation** ⭐⭐⭐⭐⭐
  - Lien : https://docs.oracle.com/en/java/javase/17/docs/api/
  - Classes de base : String, Integer, Scanner
* **Java Language Specification** ⭐⭐⭐⭐
  - Lien : https://docs.oracle.com/javase/specs/jls/se17/html/
  - Référence complète du langage

==== **Guides Pratiques**
* **Baeldung Java Basics** ⭐⭐⭐⭐⭐
  - Lien : https://www.baeldung.com/java-tutorial
  - Articles détaillés avec exemples
* **GeeksforGeeks Java** ⭐⭐⭐⭐
  - Lien : https://www.geeksforgeeks.org/java/
  - Concepts expliqués simplement

=== 🎥 Ressources Vidéo Complémentaires

==== **Chaînes YouTube Recommandées**
* **Java Brains** ⭐⭐⭐⭐⭐
  - Concepts Java expliqués clairement
  - Exemples pratiques et debugging
* **Derek Banas** ⭐⭐⭐⭐
  - "Java Tutorial" - Syntaxe complète en 1h
* **Cave of Programming** ⭐⭐⭐⭐
  - Approche très progressive pour débutants

=== 🎯 Mapping Ressources → Tâches

==== **Pour T006: Types Primitifs et Variables**
1. **FreeCodeCamp** (0h-1h) - Variables et types de base
2. **Oracle Foundations** Module 1 (1h) - Syntaxe fondamentale
3. **CodingBat Warmup-1** (30min) - 10 exercices pratiques
4. **Validation :** Tous types primitifs utilisés correctement

==== **Pour T007: Structures de Contrôle**
1. **FreeCodeCamp** (1h-2h) - Conditions et boucles
2. **OpenClassrooms** Chapitre 2-3 (1h) - Structures interactives
3. **CodingBat Logic-1** (1h) - 15 exercices progressifs
4. **Validation :** Conditions complexes et boucles imbriquées

==== **Pour T008: Méthodes et Organisation**
1. **FreeCodeCamp** (2h-3h) - Méthodes et paramètres
2. **Oracle Foundations** Module 2 (1h30) - Organisation du code
3. **CodingBat String-1** (30min) - 10 exercices méthodes
4. **Validation :** Méthodes réutilisables et bien structurées

==== **Pour T009: Projet Calculatrice**
1. **Oracle Foundations** Module 3 (1h30) - Projet complet
2. **Baeldung Scanner Tutorial** (30min) - Input utilisateur
3. **GeeksforGeeks Exception Handling** (30min) - Gestion erreurs
4. **Validation :** Application fonctionnelle et robuste

=== 🔧 Outils de Validation

==== **Compilateurs en Ligne**
* **JDoodle** - https://www.jdoodle.com/online-java-compiler/
* **Replit** - https://replit.com/languages/java
* **OnlineGDB** - https://www.onlinegdb.com/online_java_compiler

==== **Aide Communautaire**
* **Stack Overflow** ⭐⭐⭐⭐⭐
  - Tag : [java] [beginner]
* **Reddit r/learnjava** ⭐⭐⭐⭐
  - Questions débutants bienvenues
* **Discord Java Community** ⭐⭐⭐⭐
  - Aide en temps réel

== ⏱️ Estimation Temporelle

=== 📊 Répartition Détaillée

[cols="4,2,2,1", options="header"]
|===
| Activité | Temps Standard | Temps Slower | Difficulté

| **📖 Étude Ressources** | | |
| FreeCodeCamp (0h-3h) | 3h | 4h | 🟢 Facile
| Oracle Foundations (Modules 1-3) | 4h | 5h | 🟡 Moyen
| OpenClassrooms (Chapitres 1-4) | 2h | 3h | 🟢 Facile

| **💻 Pratique Coding** | | |
| T006: Types et variables | 1h | 1h30 | 🟢 Facile
| T007: Structures contrôle | 1h30 | 2h30 | 🟡 Moyen
| T008: Méthodes | 1h30 | 2h30 | 🟡 Moyen
| CodingBat exercices (35 exercices) | 2h | 3h | 🟡 Moyen

| **🚀 Projet Final** | | |
| T009: Calculatrice console | 2h | 3h | 🟡 Moyen
| Tests et debugging | 1h | 1h30 | 🟡 Moyen

| **📝 Documentation** | | |
| README et commentaires | 30min | 1h | 🟢 Facile

| **🎯 TOTAL RÉALISTE** | **18h** | **26h** | **🟡 Moyen**
|===

[WARNING]
====
**Estimation Initiale Sous-Évaluée !**

- **Estimation originale :** 6h (tâches uniquement)
- **Estimation réaliste :** 18h standard / 26h slower
- **Facteur oublié :** Temps d'apprentissage des ressources
- **Recommandation :** Prévoir 3-4 semaines pour cette User Story
====

== 🎯 Objectifs d'Apprentissage
* **Syntaxe Java** - Règles et conventions
* **Types de données** - Primitifs vs objets
* **Structures de contrôle** - Logique conditionnelle
* **Méthodes** - Modularité et réutilisabilité

== 📊 Métriques de Succès
* CodingBat : 35+ exercices réussis
* Temps développement : Calculatrice en < 2h
* Lignes de code : 150-200 lignes structurées
* Erreurs compilation : 0 erreur finale

== 🔗 Liens Connexes
* US001 - Setup Environnement (prérequis)
* US003 - Git Workflow (parallèle)
* US004 - Debugging & TDD (suivante)
* Projet : 01-calculator-console
