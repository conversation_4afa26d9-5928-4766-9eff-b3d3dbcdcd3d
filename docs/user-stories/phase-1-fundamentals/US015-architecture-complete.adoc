= US015 - Concevoir Architecture Complète
:status: Backlog
:priority: Must Have
:effort: 8 points
:epic: 🏛️ Projet Synthèse Phase 1
:sprint: S15-S16 (Semaines 15-16)
:assigned: Étudiant Java OCP

[cols="1,2", options="header"]
|===
| Propriété | Valeur
| ID        | US015
| Titre     | Concevoir architecture complète et maintenable
| Epic      | {epic}
| Sprint    | {sprint}
| Priorité  | {priority}
| Effort    | {effort}
| Statut    | {status}
|===

== 🎯 Description
En tant que développeur Java
Je veux concevoir une architecture complète et maintenable
Afin de créer un système de gestion de bibliothèque professionnel.

== ✅ Critères d'Acceptation
* [ ] Architecture en couches - Présentation, Métier, Données
* [ ] Patterns de conception - Factory, Strategy, Observer
* [ ] SOLID principles - Respect des 5 principes
* [ ] Système complet - Gestion livres, membres, emprunts

== 🛠️ Tâches Principales

=== T053: Architecture en Couches (3h)
[source,java]
----
// Couche Présentation
package com.library.presentation;
public class LibraryConsoleUI {
    private final LibraryService libraryService;
    
    public void displayMainMenu() { /* UI logic */ }
    public void handleUserInput() { /* Input handling */ }
}

// Couche Métier
package com.library.business;
public class LibraryService {
    private final BookRepository bookRepository;
    private final MemberRepository memberRepository;
    private final LoanService loanService;
    
    public void borrowBook(int memberId, int bookId) { /* Business logic */ }
}

// Couche Données
package com.library.data;
public interface BookRepository {
    void save(Book book);
    Optional<Book> findById(int id);
    List<Book> findByTitle(String title);
}

public class FileBookRepository implements BookRepository {
    // Implémentation avec fichiers
}
----

Validation :
* [ ] Séparation claire des responsabilités
* [ ] Couches bien définies
* [ ] Dépendances unidirectionnelles

=== T054: Patterns de Conception (2h30)
[source,java]
----
// Factory Pattern
public class RepositoryFactory {
    public static BookRepository createBookRepository(String type) {
        switch (type.toLowerCase()) {
            case "file": return new FileBookRepository();
            case "memory": return new InMemoryBookRepository();
            default: throw new IllegalArgumentException("Unknown type: " + type);
        }
    }
}

// Strategy Pattern
public interface SearchStrategy {
    List<Book> search(String query, List<Book> books);
}

public class TitleSearchStrategy implements SearchStrategy {
    public List<Book> search(String query, List<Book> books) {
        return books.stream()
            .filter(book -> book.getTitle().toLowerCase().contains(query.toLowerCase()))
            .collect(Collectors.toList());
    }
}

// Observer Pattern
public class LibraryEventManager {
    private final List<LibraryEventListener> listeners = new ArrayList<>();
    
    public void addListener(LibraryEventListener listener) {
        listeners.add(listener);
    }
    
    public void notifyBookBorrowed(Book book, Member member) {
        listeners.forEach(l -> l.onBookBorrowed(book, member));
    }
}
----

Validation :
* [ ] Patterns correctement implémentés
* [ ] Code flexible et extensible
* [ ] Responsabilités bien séparées

=== T055: SOLID Principles (1h30)
[source,java]
----
// Single Responsibility Principle
public class Book {
    // Responsabilité: Représenter un livre
    private String isbn, title, author;
    // Pas de logique de persistance ici
}

public class BookValidator {
    // Responsabilité: Valider les livres
    public void validate(Book book) { /* validation logic */ }
}

// Open/Closed Principle
public abstract class NotificationService {
    public abstract void sendNotification(String message, Member member);
}

public class EmailNotificationService extends NotificationService {
    // Extension sans modification de la classe de base
}

// Dependency Inversion Principle
public class LibraryService {
    private final BookRepository bookRepository;  // Dépend de l'abstraction
    
    public LibraryService(BookRepository bookRepository) {
        this.bookRepository = bookRepository;  // Injection de dépendance
    }
}
----

Validation :
* [ ] Principes SOLID respectés
* [ ] Code maintenable et extensible
* [ ] Dépendances inversées

=== T056: Tests et Documentation (1h)
[source,java]
----
@Test
public class LibraryServiceTest {
    private LibraryService libraryService;
    private BookRepository mockBookRepository;
    
    @BeforeEach
    void setUp() {
        mockBookRepository = Mockito.mock(BookRepository.class);
        libraryService = new LibraryService(mockBookRepository);
    }
    
    @Test
    void shouldBorrowAvailableBook() {
        // Given
        Book book = new Book("123", "Java Guide", "Author");
        Member member = new Member(1, "John Doe");
        when(mockBookRepository.findById(123)).thenReturn(Optional.of(book));
        
        // When
        BorrowResult result = libraryService.borrowBook(1, 123);
        
        // Then
        assertTrue(result.isSuccess());
        verify(mockBookRepository).save(book);
    }
}
----

Validation :
* [ ] Couverture > 90%
* [ ] Documentation complète
* [ ] Tests d'intégration
* [ ] Mocking approprié

== 📚 Ressources
* Clean Architecture - Uncle Bob
* Design Patterns - Patterns expliqués
* SOLID Principles - Guide détaillé

== ⏱️ Estimation : 8h | 🎯 Livrable : 07-library-system

== 🔗 Liens Connexes
* US014 - Fichiers I/O (prérequis)
* Phase 2 - Java SE 17 Avancé (suivante)
* Projet : 07-library-system
