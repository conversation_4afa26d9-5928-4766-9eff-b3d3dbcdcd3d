= 🚀 Phase 1: Fondamentaux Java - User Stories
:toc: left
:toclevels: 3
:icons: font
:source-highlighter: highlight.js

image:https://img.shields.io/badge/Phase-1%20Fundamentals-blue?style=for-the-badge[Phase]
image:https://img.shields.io/badge/Duration-16%20weeks-green?style=for-the-badge[Duration]
image:https://img.shields.io/badge/User%20Stories-15-orange?style=for-the-badge[Stories]

== 🎯 Vue d'Ensemble de la Phase 1

**Objectif Principal :** Maîtriser les fondamentaux Java et établir un environnement de développement professionnel

**Durée Standard :** 16 semaines (6h/semaine = 96h total) +
**Du<PERSON>e Slower Learner :** 20-24 semaines (4h/semaine = 80-96h total) +
**Projets :** 7 applications console progressives +
**Technologies :** Java SE 17, Maven, JUnit 5, Git, VS Code

== 📋 User Stories de la Phase 1

=== 🔧 Sprint S01-S02 : Setup & Premiers Pas (Semaines 1-2)

[cols="1,3,1,1,1", options="header"]
|===
| ID | User Story | Priorité | Effort | Statut

| link:US001-setup-environnement.adoc[US001]
| Setup environnement de développement
| Must Have
| 3
| 🔄

| link:US002-syntaxe-java-fondamentale.adoc[US002]
| Maîtriser syntaxe Java fondamentale
| Must Have
| 5
| ⏳

| link:US003-git-workflow.adoc[US003]
| Maîtriser Git workflow professionnel
| Must Have
| 2
| ⏳
|===

**Livrable :** link:../../../phase-1-fundamentals/week-01-02-environment/projects/01-calculator-console/[01-calculator-console]

=== 🐛 Sprint S03-S04 : Debugging & TDD (Semaines 3-4)

[cols="1,3,1,1,1", options="header"]
|===
| ID | User Story | Priorité | Effort | Statut

| link:US004-debugging-tdd.adoc[US004]
| Maîtriser debugging et TDD
| Must Have
| 5
| ⏳

| link:US005-tests-unitaires.adoc[US005]
| Écrire tests unitaires efficaces
| Must Have
| 3
| ⏳
|===

**Livrable :** link:../../../phase-1-fundamentals/week-03-04-debugging/projects/02-guessing-game/[02-guessing-game-tdd]

=== 🏗️ Sprint S05-S06 : POO Basics (Semaines 5-6)

[cols="1,3,1,1,1", options="header"]
|===
| ID | User Story | Priorité | Effort | Statut

| link:US006-classes-objets.adoc[US006]
| Concevoir classes et objets
| Must Have
| 5
| ⏳

| link:US007-encapsulation.adoc[US007]
| Implémenter encapsulation
| Must Have
| 3
| ⏳
|===

**Livrable :** link:../../../phase-1-fundamentals/week-05-06-poo-basics/projects/03-banking-system/[03-banking-system]

=== 🦁 Sprint S07-S08 : Héritage & Polymorphisme (Semaines 7-8)

[cols="1,3,1,1,1", options="header"]
|===
| ID | User Story | Priorité | Effort | Statut

| link:US008-heritage.adoc[US008]
| Implémenter héritage
| Must Have
| 5
| ⏳

| link:US009-polymorphisme.adoc[US009]
| Maîtriser polymorphisme
| Must Have
| 3
| ⏳
|===

**Livrable :** link:../../../phase-1-fundamentals/week-07-08-inheritance/projects/04-virtual-zoo/[04-virtual-zoo]

=== 📚 Sprint S09-S11 : Collections (Semaines 9-11)

[cols="1,3,1,1,1", options="header"]
|===
| ID | User Story | Priorité | Effort | Statut

| link:US010-collections-list.adoc[US010]
| Maîtriser ArrayList et LinkedList
| Must Have
| 5
| ⏳

| link:US011-collections-map.adoc[US011]
| Utiliser HashMap et TreeMap
| Must Have
| 5
| ⏳

| link:US012-algorithmes-tri.adoc[US012]
| Implémenter algorithmes de tri
| Should Have
| 3
| ⏳
|===

**Livrable :** link:../../../phase-1-fundamentals/week-09-11-collections/projects/05-address-book/[05-address-book]

=== ⚠️ Sprint S12-S14 : Exceptions & I/O (Semaines 12-14)

[cols="1,3,1,1,1", options="header"]
|===
| ID | User Story | Priorité | Effort | Statut

| link:US013-exceptions.adoc[US013]
| Gérer exceptions proprement
| Must Have
| 5
| ⏳

| link:US014-fichiers-io.adoc[US014]
| Manipuler fichiers et I/O
| Must Have
| 5
| ⏳
|===

**Livrable :** link:../../../phase-1-fundamentals/week-12-14-exceptions-io/projects/06-task-manager/[06-task-manager]

=== 🏛️ Sprint S15-S16 : Synthèse (Semaines 15-16)

[cols="1,3,1,1,1", options="header"]
|===
| ID | User Story | Priorité | Effort | Statut

| link:US015-architecture-complete.adoc[US015]
| Concevoir architecture complète
| Must Have
| 8
| ⏳
|===

**Livrable :** link:../../../phase-1-fundamentals/week-15-16-synthesis/projects/07-library-system/[07-library-system]

== 📊 Métriques de la Phase 1

=== Répartition des Efforts

==== Version Standard (52 points - 6h/semaine)
* **Setup & Outils :** 10 points (19%) - US001-US003, US004-US005
* **POO Fondamentale :** 16 points (31%) - US006-US009
* **Collections :** 13 points (25%) - US010-US012
* **Exceptions & I/O :** 10 points (19%) - US013-US014
* **Architecture :** 8 points (15%) - US015

==== Version Slower Learner (78 points - 4h/semaine)
* **Setup & Outils :** 15 points (19%) - US001-US003, US004-US005
* **POO Fondamentale :** 24 points (31%) - US006-US009
* **Collections :** 20 points (26%) - US010-US012
* **Exceptions & I/O :** 15 points (19%) - US013-US014
* **Architecture :** 12 points (15%) - US015

[WARNING]
====
**Pour Étudiants Slower :** Prévoir +50% de temps sur chaque User Story.
Les concepts POO et Architecture nécessitent plus de pratique et révisions.
====

=== Objectifs d'Apprentissage
* [ ] **Syntaxe Java complète** - Variables, méthodes, classes
* [ ] **POO fondamentale** - 4 piliers + interfaces
* [ ] **Collections de base** - List, Set, Map usage
* [ ] **Gestion exceptions** - Try-catch + exceptions métier
* [ ] **I/O basique** - Lecture/écriture fichiers texte
* [ ] **Tests JUnit 5** - Assertions + configuration
* [ ] **Git workflow** - Branch, commit, merge, PR

=== Technologies Maîtrisées
* **Java SE 17** - Syntaxe moderne et features
* **Maven** - Build tool et gestion dépendances
* **JUnit 5** - Framework de tests unitaires
* **VS Code** - IDE avec Extension Pack for Java
* **Git/GitHub** - Contrôle de version professionnel

== 🎯 Critères de Validation Phase 1

Pour passer à la Phase 2, l'étudiant doit :

=== Projets Complétés
* [ ] **7 projets fonctionnels** - Tous compilent et s'exécutent
* [ ] **Tests unitaires** - Couverture 85%+ moyenne
* [ ] **Documentation** - README complet pour chaque projet
* [ ] **Git workflow** - Commits propres et branches

=== Compétences Techniques
* [ ] **Certification Oracle Java Foundations** (optionnel mais recommandé)
* [ ] **Score CodingBat** - 50+ exercices réussis
* [ ] **Auto-évaluation** - 7/10 minimum sur tous les concepts

=== Portfolio GitHub
* [ ] **Repository organisé** - Structure claire et professionnelle
* [ ] **README principal** - Présentation du parcours
* [ ] **Commits réguliers** - Historique de progression visible
* [ ] **Issues/PRs** - Utilisation des outils GitHub

== 🔗 Navigation

=== Liens Utiles
* link:../../LEARNING-PLAN.md[📚 Plan d'Apprentissage Complet]
* link:../../PROGRESS-TRACKER.md[📊 Tracker de Progression]
* link:../../RESOURCES.md[📖 Ressources Gratuites]
* link:../../../README.md[🏠 Accueil du Projet]

=== Phases Suivantes
* link:../phase-2-advanced/[⚡ Phase 2: Java SE 17 Avancé] (Semaines 17-32)
* link:../phase-3-certification/[🎓 Phase 3: Certification OCP] (Semaines 33-44)
* link:../phase-4-spring/[🌱 Phase 4: Portfolio Spring Boot] (Semaines 33-50)

---

**Phase 1 créée le [Date] - Prêt pour l'apprentissage ! 🚀**
