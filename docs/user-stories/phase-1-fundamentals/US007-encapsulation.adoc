= US007 - Implémenter Encapsulation
:status: Backlog
:priority: Must Have
:effort: 3 points
:epic: 🏗️ Maîtrise POO & Architecture
:sprint: S05-S06 (Semaines 5-6)
:assigned: Étudiant Java OCP

[cols="1,2", options="header"]
|===
| Propriété | Valeur
| ID        | US007
| Titre     | Implémenter encapsulation et validation
| Epic      | {epic}
| Sprint    | {sprint}
| Priorité  | {priority}
| Effort    | {effort}
| Statut    | {status}
|===

== 🎯 Description
En tant que développeur Java
Je veux maîtriser l'encapsulation et la validation des données
Afin de créer des classes robustes et sécurisées.

== ✅ Critères d'Acceptation

=== Attributs Privés
* [ ] Tous les champs déclarés `private`
* [ ] Accès contrôlé via getters/setters
* [ ] Pas d'exposition directe des données internes
* [ ] Defensive copying pour collections

=== Getters/Setters
* [ ] Getters pour lecture des propriétés
* [ ] Setters avec validation appropriée
* [ ] Pas de setters pour propriétés immuables
* [ ] Nommage conventionnel (get/set/is)

=== Validation
* [ ] Règles métier dans les setters
* [ ] Exceptions appropriées pour données invalides
* [ ] Validation dans les constructeurs
* [ ] Messages d'erreur explicites

=== Immutabilité
* [ ] Classes immuables quand approprié
* [ ] Attributs `final` pour propriétés fixes
* [ ] Pas de setters pour objets immuables
* [ ] Defensive copying des collections

== 🛠️ Tâches Détaillées

=== T025: Encapsulation Complète (1h30)
[source,java]
----
public class Account {
    private final String accountNumber;  // Immuable après création
    private double balance;              // Modifiable avec validation
    private final Customer owner;        // Immuable après création
    private final List<Transaction> transactions; // Collection privée

    public Account(String accountNumber, Customer owner) {
        setAccountNumber(accountNumber);  // Validation dans méthode privée
        setOwner(owner);
        this.balance = 0.0;
        this.transactions = new ArrayList<>();
    }

    // Pas de setter pour accountNumber (immuable)
    public String getAccountNumber() {
        return accountNumber;
    }

    // Getter avec validation
    public double getBalance() {
        return balance;
    }

    // Setter privé avec validation
    private void setBalance(double balance) {
        if (balance < 0) {
            throw new IllegalArgumentException("Balance cannot be negative");
        }
        this.balance = balance;
    }

    // Pas de setter pour owner (immuable)
    public Customer getOwner() {
        return owner;
    }

    // Defensive copy pour les collections
    public List<Transaction> getTransactions() {
        return new ArrayList<>(transactions);  // Copie défensive
    }

    // Méthodes métier avec validation
    public void deposit(double amount) {
        validateAmount(amount, "Deposit");
        this.balance += amount;
        addTransaction(new Transaction(TransactionType.DEPOSIT, amount));
    }

    public boolean withdraw(double amount) {
        validateAmount(amount, "Withdrawal");
        if (amount > balance) {
            return false;  // Fonds insuffisants
        }
        this.balance -= amount;
        addTransaction(new Transaction(TransactionType.WITHDRAWAL, amount));
        return true;
    }

    private void validateAmount(double amount, String operation) {
        if (amount <= 0) {
            throw new IllegalArgumentException(operation + " amount must be positive");
        }
        if (Double.isNaN(amount) || Double.isInfinite(amount)) {
            throw new IllegalArgumentException(operation + " amount must be a valid number");
        }
    }

    private void addTransaction(Transaction transaction) {
        this.transactions.add(transaction);
    }

    private void setAccountNumber(String accountNumber) {
        if (accountNumber == null || accountNumber.trim().isEmpty()) {
            throw new IllegalArgumentException("Account number cannot be null or empty");
        }
        if (!accountNumber.matches("^[A-Z]{3}\\d{3}$")) {
            throw new IllegalArgumentException("Account number must follow format: ABC123");
        }
        this.accountNumber = accountNumber;
    }

    private void setOwner(Customer owner) {
        if (owner == null) {
            throw new IllegalArgumentException("Account owner cannot be null");
        }
        this.owner = owner;
    }
}
----

Validation :
* [ ] Aucun attribut public
* [ ] Validation dans tous les setters
* [ ] Getters appropriés sans exposition interne
* [ ] Defensive copying pour collections

=== T026: Validation Métier (1h)
[source,java]
----
public class Customer {
    private final int customerId;
    private String name;
    private String email;
    private final LocalDate dateOfBirth;
    private String phoneNumber;
    private Address address;

    public Customer(int customerId, String name, String email, LocalDate dateOfBirth) {
        this.customerId = validateCustomerId(customerId);
        this.name = validateAndSetName(name);
        this.email = validateAndSetEmail(email);
        this.dateOfBirth = validateDateOfBirth(dateOfBirth);
    }

    // Getters
    public int getCustomerId() { return customerId; }
    public String getName() { return name; }
    public String getEmail() { return email; }
    public LocalDate getDateOfBirth() { return dateOfBirth; }
    public String getPhoneNumber() { return phoneNumber; }
    public Address getAddress() {
        return address != null ? new Address(address) : null;  // Defensive copy
    }

    // Setters avec validation
    public void setName(String name) {
        this.name = validateAndSetName(name);
    }

    public void setEmail(String email) {
        this.email = validateAndSetEmail(email);
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = validatePhoneNumber(phoneNumber);
    }

    public void setAddress(Address address) {
        this.address = address != null ? new Address(address) : null;  // Defensive copy
    }

    // Méthodes de validation privées
    private int validateCustomerId(int customerId) {
        if (customerId <= 0) {
            throw new IllegalArgumentException("Customer ID must be positive");
        }
        return customerId;
    }

    private String validateAndSetName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Name cannot be null or empty");
        }
        if (name.length() > 100) {
            throw new IllegalArgumentException("Name cannot exceed 100 characters");
        }
        if (!name.matches("^[a-zA-Z\\s'-]+$")) {
            throw new IllegalArgumentException("Name contains invalid characters");
        }
        return name.trim();
    }

    private String validateAndSetEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            throw new IllegalArgumentException("Email cannot be null or empty");
        }
        if (!email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$")) {
            throw new IllegalArgumentException("Invalid email format");
        }
        return email.toLowerCase().trim();
    }

    private LocalDate validateDateOfBirth(LocalDate dateOfBirth) {
        if (dateOfBirth == null) {
            throw new IllegalArgumentException("Date of birth cannot be null");
        }
        if (dateOfBirth.isAfter(LocalDate.now())) {
            throw new IllegalArgumentException("Date of birth cannot be in the future");
        }
        if (dateOfBirth.isBefore(LocalDate.now().minusYears(150))) {
            throw new IllegalArgumentException("Date of birth cannot be more than 150 years ago");
        }
        return dateOfBirth;
    }

    private String validatePhoneNumber(String phoneNumber) {
        if (phoneNumber != null && !phoneNumber.trim().isEmpty()) {
            if (!phoneNumber.matches("^\\+?[1-9]\\d{1,14}$")) {
                throw new IllegalArgumentException("Invalid phone number format");
            }
        }
        return phoneNumber;
    }

    // Méthodes métier
    public int getAge() {
        return Period.between(dateOfBirth, LocalDate.now()).getYears();
    }

    public boolean isAdult() {
        return getAge() >= 18;
    }
}
----

Validation :
* [ ] Exceptions métier appropriées
* [ ] Validation complète des données
* [ ] Messages d'erreur explicites
* [ ] Règles métier respectées

=== T027: Tests de Validation (30 min)
[source,java]
----
class CustomerValidationTest {

    @Test
    void shouldCreateValidCustomer() {
        Customer customer = new Customer(1, "John Doe", "<EMAIL>",
                                       LocalDate.of(1990, 5, 15));

        assertEquals(1, customer.getCustomerId());
        assertEquals("John Doe", customer.getName());
        assertEquals("<EMAIL>", customer.getEmail());
        assertEquals(LocalDate.of(1990, 5, 15), customer.getDateOfBirth());
    }

    @Test
    void shouldThrowExceptionForInvalidCustomerId() {
        assertThrows(IllegalArgumentException.class, () -> {
            new Customer(0, "John Doe", "<EMAIL>", LocalDate.of(1990, 5, 15));
        });

        assertThrows(IllegalArgumentException.class, () -> {
            new Customer(-1, "John Doe", "<EMAIL>", LocalDate.of(1990, 5, 15));
        });
    }

    @Test
    void shouldThrowExceptionForInvalidName() {
        assertThrows(IllegalArgumentException.class, () -> {
            new Customer(1, "", "<EMAIL>", LocalDate.of(1990, 5, 15));
        });

        assertThrows(IllegalArgumentException.class, () -> {
            new Customer(1, null, "<EMAIL>", LocalDate.of(1990, 5, 15));
        });

        assertThrows(IllegalArgumentException.class, () -> {
            new Customer(1, "John123", "<EMAIL>", LocalDate.of(1990, 5, 15));
        });
    }

    @Test
    void shouldThrowExceptionForInvalidEmail() {
        assertThrows(IllegalArgumentException.class, () -> {
            new Customer(1, "John Doe", "invalid-email", LocalDate.of(1990, 5, 15));
        });

        assertThrows(IllegalArgumentException.class, () -> {
            new Customer(1, "John Doe", "", LocalDate.of(1990, 5, 15));
        });
    }

    @Test
    void shouldThrowExceptionForFutureDateOfBirth() {
        assertThrows(IllegalArgumentException.class, () -> {
            new Customer(1, "John Doe", "<EMAIL>", LocalDate.now().plusDays(1));
        });
    }

    @Test
    void shouldValidatePhoneNumber() {
        Customer customer = new Customer(1, "John Doe", "<EMAIL>",
                                       LocalDate.of(1990, 5, 15));

        // Valid phone numbers
        assertDoesNotThrow(() -> customer.setPhoneNumber("+33123456789"));
        assertDoesNotThrow(() -> customer.setPhoneNumber("123456789"));

        // Invalid phone numbers
        assertThrows(IllegalArgumentException.class, () -> {
            customer.setPhoneNumber("abc123");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            customer.setPhoneNumber("123");
        });
    }

    @Test
    void shouldCalculateAgeCorrectly() {
        Customer customer = new Customer(1, "John Doe", "<EMAIL>",
                                       LocalDate.now().minusYears(25));

        assertEquals(25, customer.getAge());
        assertTrue(customer.isAdult());
    }
}
----

Validation :
* [ ] Tests d'exceptions complets
* [ ] Validation de tous les cas limites
* [ ] Messages d'erreur vérifiés
* [ ] Couverture > 90% sur validation

== 📚 Ressources d'Apprentissage

=== 🎯 Ressources Principales (À étudier dans l'ordre)

==== **1. Concepts Encapsulation Fondamentaux (2h d'étude)**
* **Oracle Access Control Tutorial** ⭐⭐⭐⭐⭐
  - Lien : https://docs.oracle.com/javase/tutorial/java/javaOO/accesscontrol.html
  - Concepts : private, protected, public, package-private
  - Points forts : Documentation officielle avec exemples
* **OpenClassrooms - Encapsulation Java** ⭐⭐⭐⭐⭐
  - Lien : https://openclassrooms.com/fr/courses/8383791-apprenez-a-programmer-en-java
  - Chapitre encapsulation avec exercices

==== **2. Patterns Défensifs (1h30 d'apprentissage)**
* **Baeldung Encapsulation Guide** ⭐⭐⭐⭐⭐
  - Lien : https://www.baeldung.com/java-encapsulation
  - Defensive programming et validation
  - Exemples concrets avec code
* **Effective Java - Item 50** ⭐⭐⭐⭐⭐
  - Defensive copies et immutabilité
  - Disponible en bibliothèque

==== **3. Validation et Sécurité (1h d'apprentissage)**
* **Java Bean Validation** ⭐⭐⭐⭐
  - Lien : https://beanvalidation.org/
  - Annotations de validation
  - Patterns de validation robustes

=== 📖 Ressources Approfondissement

==== **Design Patterns**
* **Builder Pattern** ⭐⭐⭐⭐⭐
  - Construction d'objets complexes
  - Encapsulation de la création
* **Factory Pattern** ⭐⭐⭐⭐
  - Encapsulation de l'instanciation

==== **Bonnes Pratiques**
* **Clean Code - Chapitre Classes** ⭐⭐⭐⭐⭐
  - Principes de conception
  - Responsabilité unique
* **Java Brains - OOP Videos** ⭐⭐⭐⭐
  - Vidéos explicatives POO

=== 🔧 Outils de Validation

==== **IDE et Analysis**
* **VS Code Java** ⭐⭐⭐⭐⭐
  - Détection violations encapsulation
  - Refactoring automatique
* **SonarLint** ⭐⭐⭐⭐⭐
  - Analyse qualité code
  - Détection anti-patterns

==== **Aide Communautaire**
* **Stack Overflow** ⭐⭐⭐⭐⭐
  - Tags : [java] [encapsulation] [access-modifiers]

== ⏱️ Estimation Temporelle
[cols="3,2,1", options="header"]
|===
| Tâche | Durée | Difficulté
| Encapsulation complète | 1h30 | 🟡 Moyen
| Validation métier | 1h | 🟡 Moyen
| Tests validation | 30 min | 🟢 Facile
| **Total** | **3h** | **🟡 Moyen**
|===

== 📊 Métriques de Succès
* Aucun attribut public dans les classes
* Validation dans 100% des setters
* Tests d'exceptions complets
* Defensive copying pour collections

== 🔗 Liens Connexes
* US006 - Classes et Objets (prérequis)
* US008 - Héritage (suivante)
* US009 - Polymorphisme (suivante)
* Projet : 03-banking-system
