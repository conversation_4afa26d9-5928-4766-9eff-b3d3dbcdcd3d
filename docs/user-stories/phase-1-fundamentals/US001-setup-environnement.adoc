= US001 - Setup Environnement de Développement
:status: In Progress
:priority: Must Have
:effort: 3 points
:epic: 🚀 Fondations Solides Java
:sprint: S01-S02 (Semaines 1-2)
:assigned: Milena LP

[cols="1,2", options="header"]
|===
| Propriété | Valeur
| ID        | US001
| Titre     | Setup environnement de développement professionnel
| Epic      | {epic}
| Sprint    | {sprint}
| Priorité  | {priority}
| Effort    | {effort}
| Statut    | {status}
| Assigné à | {assigned}
|===

== 🎯 Description
En tant qu'étudiant Java débutant
Je veux configurer un environnement de développement professionnel complet
Afin de pouvoir coder efficacement et suivre les bonnes pratiques dès le début de mon apprentissage.

== ✅ Critères d'Acceptation

=== Environnement Java
* [ ] JDK 17 installé (`java -version` affiche Java 17.x.x)
* [ ] JAVA_HOME configuré correctement
* [ ] Maven installé (`mvn -version` fonctionne avec Java 17)

=== IDE et Outils
* [ ] VS Code installé (dernière version stable)
* [ ] Extension Pack for Java (6 extensions actives)
* [ ] Configuration `settings.json` optimisée
* [ ] Debugging fonctionnel (breakpoints + step-by-step)

=== Contrôle de Version
* [ ] Git installé (`git --version`)
* [ ] Configuration Git (user.name & user.email définis)
* [ ] Repository GitHub créé : *java-ocp-certification-journey*
* [ ] Clone local réussi

=== Validation Finale
* [ ] Projet Maven compilé avec succès
* [ ] Premier commit effectué
* [ ] `mvn test` passe sans erreurs

== 🛠️ Tâches Détaillées

=== T001: Installation JDK 17 (45 min)
[source,bash]
----
java -version
curl -s "https://get.sdkman.io" | bash
sdk install java 17.0.9-oracle
echo 'export JAVA_HOME=$(/usr/libexec/java_home -v 17)' >> ~/.zshrc
source ~/.zshrc
echo $JAVA_HOME
java -version
----

Validation :
* [ ] `java -version` affiche Java 17
* [ ] `javac -version` affiche Java 17
* [ ] `$JAVA_HOME` pointe sur JDK 17

=== T002: Installation Maven (30 min)
[source,bash]
----
brew install maven
curl -O https://archive.apache.org/dist/maven/maven-3/3.9.6/binaries/apache-maven-3.9.6-bin.tar.gz
mvn -version
----

Validation :
* [ ] Maven 3.9.x installé
* [ ] Java version = 17.x.x
* [ ] `mvn help:evaluate -Dexpression=java.version` = 17

=== T003: Setup VS Code (45 min)
[source,bash]
----
code --install-extension vscjava.vscode-java-pack
code --list-extensions | grep vscjava
----

Configuration `settings.json` :
[source,json]
----
{
  "java.configuration.updateBuildConfiguration": "automatic",
  "java.compile.nullAnalysis.mode": "automatic",
  "java.format.enabled": true,
  "java.format.settings.url": "https://raw.githubusercontent.com/google/styleguide/gh-pages/eclipse-java-google-style.xml",
  "editor.formatOnSave": true
}
----

Validation :
* [ ] Extensions installées
* [ ] Java Overview accessible
* [ ] Auto-complétion fonctionne
* [ ] Debugging opérationnel

=== T004: Configuration Git et GitHub (30 min)
[source,bash]
----
git config --global user.name "Votre Nom"
git config --global user.email "<EMAIL>"
git config --list | grep user
git clone https://github.com/votre-username/java-ocp-certification-journey.git
cd java-ocp-certification-journey
----

Validation :
* [ ] `git config` correct
* [ ] Repository créé
* [ ] Clone local réussi

=== T005: Test Environnement Complet (30 min)
[source,bash]
----
cp -r templates/maven-java-project/* phase-1-fundamentals/week-01-02-environment/projects/01-calculator-console/
cd phase-1-fundamentals/week-01-02-environment/projects/01-calculator-console/
mvn compile
mvn test
code .
----

Validation :
* [ ] Compilation réussie
* [ ] Tests passent
* [ ] Projet ouvert dans VS Code
* [ ] Debugging fonctionne

== 📚 Ressources d'Apprentissage

=== 🎯 Ressources Principales (À suivre dans l'ordre)

==== **1. Installation JDK 17**
* **Oracle JDK 17 Download** ⭐⭐⭐⭐⭐
  - Lien : https://www.oracle.com/java/technologies/downloads/#java17
  - Guide : https://docs.oracle.com/en/java/javase/17/install/
* **Tutoriel Vidéo Installation** ⭐⭐⭐⭐
  - FreeCodeCamp : "How to Install Java JDK 17" (15 min)
  - Java Brains : "Java Development Environment Setup"

==== **2. Configuration VS Code**
* **Extension Pack for Java** ⭐⭐⭐⭐⭐
  - Lien : https://marketplace.visualstudio.com/items?itemName=vscjava.vscode-java-pack
  - Documentation : https://code.visualstudio.com/docs/languages/java
* **Tutoriel Configuration** ⭐⭐⭐⭐⭐
  - VS Code Java Tutorial : "Getting Started with Java in VS Code" (20 min)
  - OpenClassrooms : "Configurez votre environnement de développement Java"

==== **3. Git et GitHub**
* **Git Documentation Officielle** ⭐⭐⭐⭐
  - Lien : https://git-scm.com/doc
  - Pro Git Book (gratuit) : https://git-scm.com/book
* **Tutoriels Interactifs** ⭐⭐⭐⭐⭐
  - GitHub Skills : https://skills.github.com/
  - Learn Git Branching : https://learngitbranching.js.org/

==== **4. Maven Build Tool**
* **Maven Getting Started** ⭐⭐⭐⭐⭐
  - Lien : https://maven.apache.org/guides/getting-started/
  - Baeldung Maven Tutorial : https://www.baeldung.com/maven
* **Vidéo Explicative** ⭐⭐⭐⭐
  - Java Brains : "Maven Tutorial for Beginners" (45 min)

=== 🔧 Ressources de Dépannage

==== **Problèmes Courants**
* **Stack Overflow** - Recherche d'erreurs spécifiques ⭐⭐⭐⭐⭐
* **Oracle Community** - Support officiel ⭐⭐⭐⭐
* **Reddit r/learnjava** - Aide communautaire ⭐⭐⭐⭐

==== **Validation Installation**
* **Oracle Java Foundations** ⭐⭐⭐⭐⭐
  - Module 1 : "Setting up Java Development Environment"
  - Lien : https://learn.oracle.com/ols/learning-path/oracle-java-foundations/88323/79726

== ⏱️ Estimation Temporelle Détaillée

=== 📊 Répartition Réaliste

[cols="4,2,2,1", options="header"]
|===
| Activité | Temps Standard | Temps Slower | Difficulté

| **📖 Apprentissage Préalable** | | |
| Tutoriels installation JDK | 30min | 45min | 🟢 Facile
| Vidéos VS Code Java setup | 20min | 30min | 🟢 Facile
| Git concepts de base | 30min | 45min | 🟢 Facile

| **🛠️ Installation Technique** | | |
| T001: Installation JDK 17 | 45min | 1h15 | 🟢 Facile
| T002: Installation Maven | 30min | 45min | 🟢 Facile
| T003: Setup VS Code | 45min | 1h15 | 🟡 Moyen
| T004: Configuration Git | 30min | 45min | 🟢 Facile

| **🧪 Tests et Validation** | | |
| T005: Test environnement | 30min | 45min | 🟡 Moyen
| Debugging problèmes | 1h | 2h | 🟡 Moyen
| Validation complète | 30min | 45min | 🟢 Facile

| **📝 Documentation** | | |
| README projet | 15min | 30min | 🟢 Facile
| Configuration backup | 15min | 30min | 🟢 Facile

| **🎯 TOTAL RÉALISTE** | **6h** | **9h30** | **🟡 Moyen**
|===

[WARNING]
====
**Estimation Originale Sous-Évaluée !**

- **Estimation originale :** 3h (tâches techniques uniquement)
- **Estimation réaliste :** 6h standard / 9h30 slower
- **Facteur oublié :** Apprentissage préalable + debugging
- **Recommandation :** Prévoir 1-2 semaines selon expérience
====

=== 🎯 Mapping Ressources → Tâches Spécifiques

==== **Préparation (1h30 d'étude)**
1. **Oracle JDK 17 Download Guide** - Comprendre les versions
2. **FreeCodeCamp Installation Video** - Visualiser le processus
3. **VS Code Java Documentation** - Préparer la configuration
4. **Validation :** Concepts clairs avant installation

==== **Pour T001: Installation JDK 17**
1. **Oracle Installation Guide** - Instructions officielles
2. **Java Brains Setup Video** - Démonstration pratique
3. **Stack Overflow JDK Issues** - Résolution problèmes
4. **Validation :** `java -version` et `javac -version` fonctionnels

==== **Pour T002-T003: Maven et VS Code**
1. **Maven Getting Started** - Installation et configuration
2. **VS Code Extension Pack** - Installation guidée
3. **Baeldung Maven Tutorial** - Premiers pas
4. **Validation :** Projet Maven compilable dans VS Code

==== **Pour T004-T005: Git et Tests**
1. **GitHub Skills Tutorial** - Configuration Git
2. **Learn Git Branching** - Concepts interactifs
3. **Oracle Java Foundations** - Validation environnement
4. **Validation :** Premier commit réussi

== 🚨 Problèmes Courants

[WARNING]
====
* Java non détecté → vérifier `PATH` et `JAVA_HOME`
* Maven utilise mauvaise version → forcer `JAVA_HOME`
* VS Code ne détecte pas Java → Reload Window & Reload Projects
====

== 📊 Métriques de Succès
* Temps d'installation < 3h
* Compilation Maven < 30s
* Tests unitaires passent à 100%
* Debugging fonctionne sans erreurs

== 🔗 Liens Connexes
* US002 - Syntaxe Java Fondamentale
* US003 - Git Workflow
* Projet : 01-calculator-console
* Progress Tracker
* Resources