= US010 - Maîtriser Collections List
:status: Backlog
:priority: Must Have
:effort: 5 points
:epic: 📚 Collections Fundamentales
:sprint: S09-S11 (Semaines 9-11)
:assigned: Étudiant Java OCP

[cols="1,2", options="header"]
|===
| Propriété | Valeur
| ID        | US010
| Titre     | Maîtriser ArrayList et LinkedList
| Epic      | {epic}
| Sprint    | {sprint}
| Priorité  | {priority}
| Effort    | {effort}
| Statut    | {status}
|===

== 🎯 Description
En tant que développeur Java
Je veux maîtriser les collections List (ArrayList, LinkedList)
Afin de gérer efficacement des données dynamiques.

== ✅ Critères d'Acceptation
* [ ] ArrayList maîtrisé - CRUD operations, performance
* [ ] LinkedList compris - Différences avec ArrayList
* [ ] Itération efficace - for-each, Iterator, streams basiques
* [ ] Carnet d'adresses - Application complète avec contacts

== 🛠️ Tâches Principales

=== T034: ArrayList Operations (2h)
[source,java]
----
public class ContactManager {
    private List<Contact> contacts = new ArrayList<>();

    public void addContact(Contact contact) {
        contacts.add(contact);
    }

    public List<Contact> searchByName(String name) {
        return contacts.stream()
            .filter(c -> c.getName().contains(name))
            .collect(Collectors.toList());
    }

    public void sortByName() {
        contacts.sort(Comparator.comparing(Contact::getName));
    }
}
----

Validation :
* [ ] CRUD complet
* [ ] Recherche et tri fonctionnels
* [ ] Performance comprise

=== T035: LinkedList vs ArrayList (1h)
[source,java]
----
public void comparePerformance() {
    List<String> arrayList = new ArrayList<>();
    List<String> linkedList = new LinkedList<>();

    // Test insertions au début
    long start = System.nanoTime();
    for (int i = 0; i < 10000; i++) {
        arrayList.add(0, "Item " + i);  // O(n)
    }
    long arrayTime = System.nanoTime() - start;

    // Analyser les résultats
}
----

Validation :
* [ ] Différences de performance comprises
* [ ] Cas d'usage appropriés identifiés

=== T036: Itération et Manipulation (1h30)
[source,java]
----
// For-each (recommandé)
for (Contact contact : contacts) {
    System.out.println(contact);
}

// Iterator (pour suppression sécurisée)
Iterator<Contact> it = contacts.iterator();
while (it.hasNext()) {
    if (it.next().isInactive()) {
        it.remove();
    }
}

// Streams (Java 8+)
contacts.stream()
    .filter(Contact::isActive)
    .forEach(System.out::println);
----

Validation :
* [ ] Toutes les techniques maîtrisées
* [ ] Suppression sécurisée
* [ ] Streams basiques utilisés

=== T037: Application Carnet d'Adresses (30 min)
Interface console complète avec menu et opérations CRUD.

Validation :
* [ ] Application complète et fonctionnelle
* [ ] Interface utilisateur intuitive

== 📚 Ressources d'Apprentissage

=== 🎯 Ressources Principales (À étudier dans l'ordre)

==== **1. Collections Framework Fondamentaux (2h d'étude)**
* **Oracle Collections Tutorial** ⭐⭐⭐⭐⭐
  - Lien : https://docs.oracle.com/javase/tutorial/collections/
  - Concepts : List, ArrayList, LinkedList, Iterator
  - Points forts : Documentation officielle complète
* **OpenClassrooms - Collections Java** ⭐⭐⭐⭐⭐
  - Lien : https://openclassrooms.com/fr/courses/8383791-apprenez-a-programmer-en-java
  - Chapitre collections avec exercices

==== **2. ArrayList vs LinkedList (1h30 d'apprentissage)**
* **Baeldung ArrayList Guide** ⭐⭐⭐⭐⭐
  - Lien : https://www.baeldung.com/java-arraylist
  - Performance, méthodes, bonnes pratiques
* **Baeldung LinkedList Guide** ⭐⭐⭐⭐⭐
  - Lien : https://www.baeldung.com/java-linkedlist
  - Comparaison performance avec ArrayList

==== **3. Algorithmes et Performance (1h d'apprentissage)**
* **Collections Performance Analysis** ⭐⭐⭐⭐
  - Big O notation pour collections
  - Choix de la bonne collection
* **Java Collections Cheat Sheet** ⭐⭐⭐⭐
  - Référence rapide performance

=== 📖 Ressources Approfondissement

==== **Concepts Avancés**
* **Iterator et ListIterator** ⭐⭐⭐⭐
  - Parcours sécurisé des collections
  - Modification pendant itération
* **Collections.sort() Algorithms** ⭐⭐⭐⭐
  - Algorithmes de tri intégrés

==== **Exercices Pratiques**
* **Collections Katas** ⭐⭐⭐⭐
  - Exercices progressifs
  - Manipulation de listes

=== 🔧 Outils de Validation

==== **IDE et Debugging**
* **VS Code Java** ⭐⭐⭐⭐⭐
  - Visualisation contenu collections
  - Debugging itérations
* **JProfiler** ⭐⭐⭐⭐
  - Analyse performance collections

==== **Aide Communautaire**
* **Stack Overflow** ⭐⭐⭐⭐⭐
  - Tags : [java] [arraylist] [collections] [list]

== ⏱️ Estimation Temporelle Détaillée

=== 📊 Répartition Réaliste

[cols="4,2,2,1", options="header"]
|===
| Activité | Temps Standard | Temps Slower | Difficulté

| **📖 Apprentissage Théorique** | | |
| Collections Framework fondamentaux | 2h | 3h | 🟡 Moyen
| ArrayList vs LinkedList | 1h30 | 2h | 🟡 Moyen
| Algorithmes et performance | 1h | 1h30 | 🟡 Moyen

| **💻 Pratique Guidée** | | |
| Baeldung exercises | 1h30 | 2h | 🟡 Moyen
| Collections katas | 1h | 1h30 | 🟡 Moyen

| **🛠️ Implémentation Projet** | | |
| T034: Gestion contacts | 1h30 | 2h | 🟡 Moyen
| T035: Recherche et tri | 1h | 1h30 | 🟡 Moyen
| T036: Interface utilisateur | 1h | 1h30 | 🟡 Moyen

| **🧪 Tests et Validation** | | |
| Tests unitaires collections | 1h | 1h30 | 🟡 Moyen
| Tests performance | 30min | 1h | 🟡 Moyen

| **🎯 TOTAL RÉALISTE** | **11h** | **16h** | **🟡 Moyen**
|===

[WARNING]
====
**Collections = Fondation Java !**

- **Estimation originale :** 5h (sous-estimée)
- **Estimation réaliste :** 11h standard / 16h slower
- **Pourquoi plus long :**
  * Apprentissage Collections Framework complet
  * Compréhension performance ArrayList vs LinkedList
  * Maîtrise algorithmes de tri
  * Pratique manipulation de données
- **Recommandation :** Prévoir 2 semaines
====

=== 🎯 Mapping Ressources → Tâches Spécifiques

==== **Préparation (4h30 d'apprentissage)**
1. **Oracle Collections Tutorial** - Framework complet
2. **Baeldung ArrayList/LinkedList** - Comparaison performance
3. **Performance Analysis** - Big O et choix collections
4. **Collections Katas** - Pratique manipulation
5. **Validation :** Collections Framework maîtrisé

==== **Pour T034: Gestion Contacts**
1. **ArrayList operations** - Add, remove, get
2. **Iterator patterns** - Parcours sécurisé
3. **Collections utility** - Méthodes utilitaires
4. **Validation :** CRUD complet sur contacts

==== **Pour T035: Recherche et Tri**
1. **Collections.sort()** - Tri intégré
2. **Custom Comparator** - Tri personnalisé
3. **Binary search** - Recherche efficace
4. **Validation :** Recherche et tri fonctionnels

==== **Pour T036: Interface Utilisateur**
1. **List to String** - Affichage collections
2. **Input validation** - Saisie utilisateur
3. **Error handling** - Gestion erreurs
4. **Validation :** Interface intuitive

== 🎯 Livrable : 05-address-book

== 🔗 Liens Connexes
* US009 - Polymorphisme (prérequis)
* US011 - Collections Map (suivante)
* Projet : 05-address-book
