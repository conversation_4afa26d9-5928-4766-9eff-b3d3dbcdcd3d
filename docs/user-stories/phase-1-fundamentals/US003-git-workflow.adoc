= US003 - Maîtriser Git Workflow Professionnel
:status: Backlog
:priority: Must Have
:effort: 2 points
:epic: 🚀 Fondations Solides Java
:sprint: S01-S02 (Semaines 1-2)
:assigned: Étudiant Java OCP

[cols="1,2", options="header"]
|===
| Propriété | Valeur
| ID        | US003
| Titre     | Maîtriser Git workflow professionnel
| Epic      | {epic}
| Sprint    | {sprint}
| Priorité  | {priority}
| Effort    | {effort}
| Statut    | {status}
| Dépendances | US001 (Setup environnement)
|===

== 🎯 Description
En tant qu'étudiant Java débutant
Je veux maîtriser Git et GitHub pour versionner mon code proprement
Afin de pouvoir collaborer efficacement, sauvegarder mon travail et démontrer ma progression.

== ✅ Critères d'Acceptation

=== Commandes Git Essentielles
* [ ] git add/commit - Staging et commits atomiques maîtrisés
* [ ] git push/pull - Synchronisation avec repository distant
* [ ] git status/log - Suivi de l'état et historique
* [ ] git diff - Visualisation des changements

=== Workflow Professionnel
* [ ] Messages de commit - Convention et clarté respectées
* [ ] Commits atomiques - Une fonctionnalité = un commit
* [ ] Branches - Feature branches et merge maîtrisés
* [ ] .gitignore - Fichiers appropriés exclus

=== Documentation GitHub
* [ ] README.md principal - Présentation du parcours OCP
* [ ] README projets - Documentation de chaque projet
* [ ] Issues - Utilisation pour tracker les tâches
* [ ] Releases - Tags pour marquer les étapes importantes

=== Portfolio Professionnel
* [ ] Structure organisée - Dossiers et fichiers logiques
* [ ] Historique propre - Commits réguliers et descriptifs
* [ ] Présentation - Repository attractif pour recruteurs
* [ ] Progression visible - Évolution des compétences tracée

== 🛠️ Tâches Détaillées

=== T010: Maîtriser Commandes Git de Base (45 min)
[source,bash]
----
# Workflow de base
git status                    # Voir l'état des fichiers
git add .                     # Ajouter tous les changements
git add src/main/java/        # Ajouter un dossier spécifique
git commit -m "feat: add Calculator class"  # Commit avec message

# Synchronisation
git push origin main          # Envoyer vers GitHub
git pull origin main          # Récupérer les changements

# Historique et différences
git log --oneline            # Historique condensé
git diff                     # Voir les changements non stagés
git diff --staged            # Voir les changements stagés
----

Exercices pratiques :
1. Créer 5 commits avec différents types de changements
2. Utiliser `git diff` avant chaque commit
3. Corriger un message avec `git commit --amend`
4. Visualiser l'historique avec `git log --graph`

Validation :
* [ ] 10+ commits dans le repository
* [ ] Messages suivent les conventions
* [ ] `git status` toujours propre après commits
* [ ] Synchronisation GitHub fonctionnelle

=== T011: Convention de Messages de Commit (30 min)
Format recommandé :
----
<type>(<scope>): <description>

<body optionnel>

<footer optionnel>
----

Types de commits :
[source,bash]
----
feat:     # Nouvelle fonctionnalité
fix:      # Correction de bug
docs:     # Documentation
style:    # Formatage, pas de changement de code
refactor: # Refactoring sans changement de fonctionnalité
test:     # Ajout ou modification de tests
chore:    # Tâches de maintenance
----

Exemples concrets :
[source,bash]
----
git commit -m "feat(calculator): add basic arithmetic operations"
git commit -m "fix(calculator): handle division by zero error"
git commit -m "docs(readme): add installation instructions"
git commit -m "test(calculator): add unit tests for all operations"
git commit -m "refactor(ui): extract menu display to separate method"
----

Validation :
* [ ] Tous les commits suivent la convention
* [ ] Types appropriés utilisés
* [ ] Descriptions claires et concises
* [ ] Scopes cohérents avec la structure

=== T012: Workflow avec Branches (45 min)
Stratégie de branches :
----
main                    # Branche principale (stable)
├── feature/calculator  # Développement calculatrice
├── feature/tests      # Ajout des tests
└── hotfix/division    # Correction urgente
----

[source,bash]
----
# Créer et basculer sur une nouvelle branche
git checkout -b feature/calculator

# Voir toutes les branches
git branch -a

# Basculer entre branches
git checkout main
git checkout feature/calculator

# Merger une branche
git checkout main
git merge feature/calculator

# Supprimer une branche
git branch -d feature/calculator
----

Workflow recommandé :
1. Créer branche pour chaque fonctionnalité
2. Développer sur la branche feature
3. Tester que tout fonctionne
4. Merger vers main
5. Supprimer la branche feature

Validation :
* [ ] 3+ branches créées et mergées
* [ ] Historique Git propre avec merges
* [ ] Aucun conflit de merge
* [ ] Branches supprimées après merge

=== T013: Configuration .gitignore et Documentation (30 min)
Fichier .gitignore pour Java/Maven :
[source,gitignore]
----
# Compiled class files
*.class
target/

# IDE files
.vscode/settings.json
.idea/
*.iml

# OS files
.DS_Store
Thumbs.db

# Logs
*.log

# Temporary files
*.tmp
*.swp
----

README.md principal :
[source,markdown]
----
# 🎯 Java OCP Certification Journey

## 📚 À Propos
Parcours d'apprentissage Java SE 17 pour obtenir la certification Oracle OCP.

## 🚀 Progression Actuelle
- **Phase 1:** Fondamentaux Java (En cours)
- **Projet actuel:** Calculatrice Console

## 📁 Structure du Repository
- `phase-1-fundamentals/` - Projets de base Java
- `docs/` - Documentation et guides
- `templates/` - Templates de projets

## 🛠️ Technologies
- Java SE 17, Maven, JUnit 5, VS Code

## 📊 Métriques
- **Projets complétés:** 0/19
- **Heures d'étude:** 4h/312h
- **Commits:** 15+
----

Validation :
* [ ] .gitignore configuré et fonctionnel
* [ ] README principal informatif et attractif
* [ ] README projet avec instructions claires
* [ ] Aucun fichier inapproprié dans Git

== 📚 Ressources d'Apprentissage

=== 🎯 Ressources Principales (À étudier dans l'ordre)

==== **1. Concepts Fondamentaux (1h30 d'étude)**
* **Pro Git Book - Chapitre 1-3** ⭐⭐⭐⭐⭐
  - Lien : https://git-scm.com/book/fr/v2
  - Concepts : Repository, commit, branch, merge
  - Points forts : Gratuit, complet, français disponible
* **GitHub Skills - Introduction to GitHub** ⭐⭐⭐⭐⭐
  - Lien : https://skills.github.com/
  - Tutoriels interactifs et progressifs

==== **2. Pratique Visuelle (1h de manipulation)**
* **Learn Git Branching** ⭐⭐⭐⭐⭐
  - Lien : https://learngitbranching.js.org/
  - Visualisation interactive des concepts
  - Exercices progressifs et ludiques
* **Atlassian Git Tutorials** ⭐⭐⭐⭐⭐
  - Lien : https://www.atlassian.com/git/tutorials
  - Guides détaillés avec exemples

==== **3. Conventions Professionnelles (30min)**
* **Conventional Commits** ⭐⭐⭐⭐⭐
  - Lien : https://www.conventionalcommits.org/
  - Convention messages de commit
* **GitHub Flow Guide** ⭐⭐⭐⭐
  - Lien : https://guides.github.com/introduction/flow/
  - Workflow simple et efficace

=== 📖 Ressources de Référence

==== **Documentation Officielle**
* **Git Documentation** ⭐⭐⭐⭐⭐
  - Lien : https://git-scm.com/doc
  - Référence complète des commandes
* **GitHub Docs** ⭐⭐⭐⭐⭐
  - Lien : https://docs.github.com/
  - Fonctionnalités GitHub spécifiques

=== 🔧 Outils de Validation

==== **Aide Communautaire**
* **Stack Overflow** ⭐⭐⭐⭐⭐
  - Tags : [git] [github] [version-control]
* **Reddit r/git** ⭐⭐⭐⭐
  - Communauté Git active

== ⏱️ Estimation Temporelle Détaillée

=== 📊 Répartition Réaliste

[cols="4,2,2,1", options="header"]
|===
| Activité | Temps Standard | Temps Slower | Difficulté

| **📖 Apprentissage Théorique** | | |
| Pro Git Book (Chapitres 1-3) | 1h30 | 2h | 🟡 Moyen
| GitHub Skills tutorials | 1h | 1h30 | 🟢 Facile
| Conventional Commits | 30min | 45min | 🟢 Facile

| **💻 Pratique Interactive** | | |
| Learn Git Branching | 1h | 1h30 | 🟡 Moyen
| Atlassian tutorials | 30min | 45min | 🟢 Facile

| **🛠️ Implémentation Projet** | | |
| T010: Commandes de base | 45min | 1h | 🟢 Facile
| T011: Convention commits | 30min | 45min | 🟢 Facile
| T012: Workflow branches | 45min | 1h15 | 🟡 Moyen
| T013: Documentation | 30min | 45min | 🟢 Facile

| **🧪 Tests et Validation** | | |
| Validation workflow | 30min | 45min | 🟡 Moyen
| Debugging Git issues | 30min | 1h | 🟡 Moyen

| **🎯 TOTAL RÉALISTE** | **7h** | **10h30** | **🟡 Moyen**
|===

[WARNING]
====
**Estimation Originale Sous-Évaluée !**

- **Estimation originale :** 2h30 (tâches techniques uniquement)
- **Estimation réaliste :** 7h standard / 10h30 slower
- **Facteur oublié :** Apprentissage concepts Git + pratique
- **Recommandation :** Prévoir 1-2 semaines selon expérience
====

== 🎯 Bonnes Pratiques
* **Commits atomiques** - Une fonctionnalité = un commit
* **Commits fréquents** - Plusieurs fois par jour
* **Messages descriptifs** - Clairs et informatifs
* **Branches courtes** - Durée de vie limitée
* **Documentation à jour** - Synchronisée avec le code

== 📊 Métriques de Succès
* Commits réguliers : 1+ commit par jour de travail
* Messages conformes : 100% suivent la convention
* Branches utilisées : 3+ branches créées et mergées
* Historique propre : Pas de commits "WIP" ou "fix"

== 🔗 Liens Connexes
* US001 - Setup Environnement (prérequis)
* US002 - Syntaxe Java (parallèle)
* US004 - Debugging & TDD (suivante)
* Projet : 01-calculator-console
