= US014 - Manipuler Fichiers et I/O
:status: Backlog
:priority: Must Have
:effort: 5 points
:epic: ⚠️ Exceptions & I/O
:sprint: S12-S14 (Semaines 12-14)
:assigned: Étudiant Java OCP

[cols="1,2", options="header"]
|===
| Propriété | Valeur
| ID        | US014
| Titre     | Manipuler fichiers et I/O efficacement
| Epic      | {epic}
| Sprint    | {sprint}
| Priorité  | {priority}
| Effort    | {effort}
| Statut    | {status}
|===

== 🎯 Description
En tant que développeur Java
Je veux maîtriser la manipulation de fichiers et I/O
Afin de persister et charger des données efficacement.

== ✅ Critères d'Acceptation
* [ ] Lecture/écriture fichiers - Texte et binaire
* [ ] NIO.2 (java.nio.file) - API moderne maîtrisée
* [ ] Sérialisation - Objets vers fichiers
* [ ] Persistance complète - Sauvegarde/chargement task manager

== 🛠️ Tâches Principales

=== T049: Files API (NIO.2) (2h)
[source,java]
----
public class TaskFileManager {
    private final Path tasksFile = Paths.get("data", "tasks.txt");
    
    public void ensureDataDirectory() throws IOException {
        Path dataDir = tasksFile.getParent();
        if (!Files.exists(dataDir)) {
            Files.createDirectories(dataDir);
        }
    }
    
    public void saveTasks(List<Task> tasks) throws IOException {
        ensureDataDirectory();
        
        try (BufferedWriter writer = Files.newBufferedWriter(
                tasksFile, StandardCharsets.UTF_8,
                StandardOpenOption.CREATE, 
                StandardOpenOption.TRUNCATE_EXISTING)) {
            
            for (Task task : tasks) {
                writer.write(taskToString(task));
                writer.newLine();
            }
        }
    }
    
    public List<Task> loadTasks() throws IOException {
        if (!Files.exists(tasksFile)) {
            return new ArrayList<>();
        }
        
        return Files.lines(tasksFile, StandardCharsets.UTF_8)
                   .map(this::stringToTask)
                   .filter(Objects::nonNull)
                   .collect(Collectors.toList());
    }
}
----

Validation :
* [ ] Fichiers créés, lus et écrits correctement
* [ ] NIO.2 API utilisée
* [ ] Gestion des chemins avec Path

=== T050: Formats de Données (1h30)
[source,java]
----
public class TaskSerializer {
    
    // Format CSV simple
    public String taskToCsv(Task task) {
        return String.join(",",
            String.valueOf(task.getId()),
            escapeCommas(task.getTitle()),
            task.getStatus().name(),
            task.getCreatedDate().toString()
        );
    }
    
    public Task csvToTask(String csvLine) {
        String[] parts = csvLine.split(",");
        if (parts.length != 4) {
            throw new IllegalArgumentException("Invalid CSV format");
        }
        
        return new Task(
            Integer.parseInt(parts[0]),
            parts[1],
            TaskStatus.valueOf(parts[2]),
            LocalDate.parse(parts[3])
        );
    }
    
    // Format JSON simple (sans bibliothèque)
    public String taskToJson(Task task) {
        return String.format(
            "{\"id\":%d,\"title\":\"%s\",\"status\":\"%s\",\"date\":\"%s\"}",
            task.getId(),
            escapeJson(task.getTitle()),
            task.getStatus(),
            task.getCreatedDate()
        );
    }
}
----

Validation :
* [ ] Formats CSV et JSON fonctionnels
* [ ] Sérialisation/désérialisation
* [ ] Gestion des caractères spéciaux

=== T051: Gestion d'Erreurs I/O (1h)
[source,java]
----
public class RobustFileManager {
    
    public boolean saveTasksSafely(List<Task> tasks) {
        Path tempFile = null;
        try {
            // Écrire dans fichier temporaire d'abord
            tempFile = Files.createTempFile("tasks", ".tmp");
            saveTasksToFile(tasks, tempFile);
            
            // Remplacer le fichier original atomiquement
            Files.move(tempFile, tasksFile, 
                StandardCopyOption.REPLACE_EXISTING,
                StandardCopyOption.ATOMIC_MOVE);
            
            return true;
            
        } catch (IOException e) {
            logger.error("Failed to save tasks", e);
            
            // Nettoyer le fichier temporaire
            if (tempFile != null) {
                try {
                    Files.deleteIfExists(tempFile);
                } catch (IOException cleanupError) {
                    logger.warn("Failed to cleanup temp file", cleanupError);
                }
            }
            return false;
        }
    }
}
----

Validation :
* [ ] Pas de perte de données
* [ ] Récupération d'erreurs
* [ ] Opérations atomiques

=== T052: Configuration et Propriétés (30 min)
[source,java]
----
public class TaskManagerConfig {
    private final Properties properties = new Properties();
    private final Path configFile = Paths.get("config.properties");
    
    public void loadConfig() throws IOException {
        if (Files.exists(configFile)) {
            try (InputStream input = Files.newInputStream(configFile)) {
                properties.load(input);
            }
        } else {
            createDefaultConfig();
        }
    }
    
    public void createDefaultConfig() throws IOException {
        properties.setProperty("data.directory", "data");
        properties.setProperty("backup.enabled", "true");
        properties.setProperty("max.tasks", "1000");
        
        try (OutputStream output = Files.newOutputStream(configFile)) {
            properties.store(output, "Task Manager Configuration");
        }
    }
}
----

Validation :
* [ ] Configuration persistante et modifiable
* [ ] Fichiers properties gérés
* [ ] Valeurs par défaut

== 📚 Ressources
* Oracle File I/O - Tutorial officiel
* NIO.2 Guide - API moderne
* Java I/O Best Practices - Bonnes pratiques

== ⏱️ Estimation : 5h | 🎯 Livrable : Persistance complète task-manager

== 🔗 Liens Connexes
* US013 - Exceptions (prérequis)
* US015 - Architecture Complète (suivante)
* Projet : 06-task-manager
