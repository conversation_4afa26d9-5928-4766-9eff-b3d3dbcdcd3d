= US008 - Implémenter Héritage
:status: Backlog
:priority: Must Have
:effort: 5 points
:epic: 🏗️ Maîtrise POO & Architecture
:sprint: S07-S08 (Semaines 7-8)
:assigned: Étudiant Java OCP

[cols="1,2", options="header"]
|===
| Propriété | Valeur
| ID        | US008
| Titre     | Implémenter héritage et hiérarchies
| Epic      | {epic}
| Sprint    | {sprint}
| Priorité  | {priority}
| Effort    | {effort}
| Statut    | {status}
|===

== 🎯 Description
En tant que développeur Java
Je veux maîtriser l'héritage et les hiérarchies de classes
Afin de créer du code réutilisable et bien structuré.

== ✅ Critères d'Acceptation
* [ ] Hiérarchie animaux - Classes Animal, Mammal, Bird, etc.
* [ ] Héritage correct - extends, super(), @Override
* [ ] Méthodes abstraites - Classes abstraites fonctionnelles
* [ ] Zoo virtuel - Système complet avec différents animaux

== 🛠️ Tâches Principales

=== T028: Hiérarchie de Classes (2h)
[source,java]
----
public abstract class Animal {
    protected String name;
    protected int age;
    protected String species;

    public Animal(String name, int age, String species) {
        this.name = name;
        this.age = age;
        this.species = species;
    }

    public abstract void makeSound();
    public abstract void move();

    public void eat() {
        System.out.println(name + " is eating.");
    }

    public void sleep() {
        System.out.println(name + " is sleeping.");
    }
}

public class Lion extends Animal {
    private String prideRole;

    public Lion(String name, int age, String prideRole) {
        super(name, age, "Lion");
        this.prideRole = prideRole;
    }

    @Override
    public void makeSound() {
        System.out.println(name + " roars loudly!");
    }

    @Override
    public void move() {
        System.out.println(name + " prowls through the savanna.");
    }

    public void hunt() {
        System.out.println(name + " is hunting prey.");
    }
}
----

Validation :
* [ ] Hiérarchie complète avec 5+ animaux
* [ ] Méthodes abstraites implémentées
* [ ] super() utilisé correctement
* [ ] @Override sur toutes les redéfinitions

=== T029: Classes Abstraites et Interfaces (1h30)
[source,java]
----
public interface Flyable {
    void fly();
    default void land() {
        System.out.println("Landing gracefully...");
    }
}

public interface Swimmable {
    void swim();
    void dive();
}

public class Eagle extends Bird implements Flyable {
    public Eagle(String name, int age) {
        super(name, age, "Eagle");
    }

    @Override
    public void fly() {
        System.out.println(name + " soars high in the sky!");
    }

    @Override
    public void makeSound() {
        System.out.println(name + " screeches!");
    }
}
----

Validation :
* [ ] Interfaces et classes abstraites utilisées
* [ ] Méthodes default dans interfaces
* [ ] Implémentations multiples d'interfaces

=== T030: Zoo Management System (1h30)
[source,java]
----
public class Zoo {
    private List<Animal> animals;
    private String name;

    public Zoo(String name) {
        this.name = name;
        this.animals = new ArrayList<>();
    }

    public void addAnimal(Animal animal) {
        animals.add(animal);
        System.out.println(animal.getName() + " added to " + name);
    }

    public void feedAllAnimals() {
        System.out.println("Feeding time at " + name + "!");
        for (Animal animal : animals) {
            animal.eat();
        }
    }

    public void makeAllSounds() {
        System.out.println("Animal sounds at " + name + ":");
        for (Animal animal : animals) {
            animal.makeSound();
        }
    }
}
----

Validation :
* [ ] Système fonctionnel avec polymorphisme
* [ ] Collections d'objets hérités
* [ ] Méthodes utilisant le polymorphisme

== 📚 Ressources d'Apprentissage

=== 🎯 Ressources Principales (Ordre d'étude recommandé)

==== **1. Concepts Théoriques (2h d'étude)**
* **Oracle Inheritance Tutorial** ⭐⭐⭐⭐⭐
  - Lien : https://docs.oracle.com/javase/tutorial/java/IandI/subclasses.html
  - Concepts : extends, super, @Override
  - Exemples progressifs et clairs
* **OpenClassrooms - Héritage en Java** ⭐⭐⭐⭐⭐
  - Lien : https://openclassrooms.com/fr/courses/8383791-apprenez-a-programmer-en-java
  - Chapitre : "Utilisez l'héritage en Java"
  - Exercices interactifs inclus

==== **2. Vidéos Explicatives (1h30 de visionnage)**
* **Java Brains - Inheritance in Java** ⭐⭐⭐⭐⭐
  - Série complète sur l'héritage
  - Exemples concrets et debugging
* **Derek Banas - Java Inheritance** ⭐⭐⭐⭐
  - Explication claire en 30 min
  - Lien : https://www.youtube.com/watch?v=wzW-251bGgM
* **Cave of Programming - OOP Inheritance** ⭐⭐⭐⭐
  - Approche très progressive pour débutants

==== **3. Pratique Guidée (2h de coding)**
* **Baeldung Inheritance Guide** ⭐⭐⭐⭐⭐
  - Lien : https://www.baeldung.com/java-inheritance
  - Exemples pratiques step-by-step
  - Patterns et anti-patterns
* **GeeksforGeeks Inheritance** ⭐⭐⭐⭐
  - Lien : https://www.geeksforgeeks.org/inheritance-in-java/
  - Exercices avec solutions
* **CodingBat Inheritance Problems** ⭐⭐⭐⭐
  - Section : Inheritance et Polymorphism
  - Exercices progressifs

=== 📖 Ressources Approfondissement

==== **Documentation Avancée**
* **Java SE 17 API - Object Class** ⭐⭐⭐⭐⭐
  - Lien : https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html
  - Méthodes héritées par défaut
* **Effective Java - Item 19** ⭐⭐⭐⭐⭐
  - "Design and document for inheritance or else prohibit it"
  - Bonnes pratiques avancées

==== **Exercices Supplémentaires**
* **HackerRank OOP Problems** ⭐⭐⭐⭐
  - Section : Object Oriented Programming
  - Problèmes réels d'héritage
* **LeetCode OOP Questions** ⭐⭐⭐
  - Design patterns avec héritage

=== 🎥 Ressources Visuelles

==== **Diagrammes et Schémas**
* **UML Class Diagrams** ⭐⭐⭐⭐
  - Visualisation des hiérarchies
  - Outils : draw.io, PlantUML
* **Java Inheritance Tree Visualizer** ⭐⭐⭐
  - Outils en ligne pour visualiser l'héritage

=== 🔧 Outils de Validation

==== **Debugging et Tests**
* **VS Code Java Debugger** ⭐⭐⭐⭐⭐
  - Step-through inheritance calls
  - Visualisation de la pile d'appels
* **JUnit 5 Testing** ⭐⭐⭐⭐⭐
  - Tests de comportement hérité
  - Validation polymorphisme

==== **Aide Communautaire Spécialisée**
* **Stack Overflow** ⭐⭐⭐⭐⭐
  - Tags : [java] [inheritance] [super] [override]
* **Reddit r/learnjava** ⭐⭐⭐⭐
  - Questions conceptuelles bienvenues
* **Oracle Community Forums** ⭐⭐⭐⭐
  - Support officiel pour concepts avancés

== ⏱️ Estimation Temporelle Détaillée

=== 📊 Répartition Réaliste

[cols="4,2,2,1", options="header"]
|===
| Activité | Temps Standard | Temps Slower | Difficulté

| **📖 Apprentissage Théorique** | | |
| Oracle Inheritance Tutorial | 1h | 1h30 | 🟡 Moyen
| OpenClassrooms Héritage | 1h | 1h30 | 🟡 Moyen
| Vidéos Java Brains | 1h30 | 2h | 🟡 Moyen

| **💻 Pratique Guidée** | | |
| Baeldung Examples | 1h | 1h30 | 🟡 Moyen
| GeeksforGeeks Exercices | 1h | 1h30 | 🟡 Moyen
| CodingBat Inheritance | 1h | 1h30 | 🟡 Moyen

| **🛠️ Développement Projet** | | |
| T028: Hiérarchie Classes | 2h | 3h | 🔴 Difficile
| T029: Abstraites/Interfaces | 1h30 | 2h30 | 🔴 Difficile
| T030: Zoo Management | 1h30 | 2h30 | 🟡 Moyen

| **🧪 Tests et Debugging** | | |
| Tests unitaires | 1h | 1h30 | 🟡 Moyen
| Debugging héritage | 1h | 2h | 🔴 Difficile

| **📝 Documentation** | | |
| UML Diagrammes | 30min | 1h | 🟢 Facile
| README projet | 30min | 1h | 🟢 Facile

| **🎯 TOTAL RÉALISTE** | **14h** | **21h** | **🔴 Difficile**
|===

[IMPORTANT]
====
**Héritage = Concept Paradigmatique Difficile !**

- **Estimation originale :** 5h (largement sous-estimée)
- **Estimation réaliste :** 14h standard / 21h slower
- **Pourquoi plus long :**
  * Nouveau paradigme de pensée
  * Debugging complexe (super, override)
  * Concepts abstraits difficiles à visualiser
- **Recommandation :** Prévoir 2 semaines complètes
====

=== 🎯 Mapping Ressources → Tâches Spécifiques

==== **Préparation (3-4h d'étude)**
1. **Oracle Inheritance Tutorial** - Concepts de base
2. **Java Brains Videos** - Visualisation des concepts
3. **OpenClassrooms** - Exercices interactifs
4. **Validation :** Quiz de compréhension réussi

==== **Pour T028: Hiérarchie de Classes**
1. **Baeldung Inheritance Guide** - Exemples step-by-step
2. **UML Class Diagrams** - Visualiser la hiérarchie
3. **VS Code Debugger** - Comprendre les appels super()
4. **Validation :** 5+ animaux avec héritage correct

==== **Pour T029: Classes Abstraites et Interfaces**
1. **Oracle Abstract Classes Tutorial** - Concepts avancés
2. **GeeksforGeeks Examples** - Patterns concrets
3. **CodingBat Problems** - Exercices pratiques
4. **Validation :** Interfaces multiples implémentées

==== **Pour T030: Zoo Management System**
1. **Design Patterns Examples** - Architecture propre
2. **JUnit 5 Testing** - Tests de polymorphisme
3. **Stack Overflow** - Résolution problèmes spécifiques
4. **Validation :** Système complet fonctionnel

🎯 **Livrable Final :** 04-virtual-zoo

== 🔗 Liens Connexes
* US007 - Encapsulation (prérequis)
* US009 - Polymorphisme (suivante)
* Projet : 04-virtual-zoo
