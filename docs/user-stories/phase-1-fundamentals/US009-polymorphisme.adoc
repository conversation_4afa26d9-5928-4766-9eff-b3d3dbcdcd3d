= US009 - Maîtriser Polymorphisme
:status: Backlog
:priority: Must Have
:effort: 3 points
:epic: 🏗️ Maîtrise POO & Architecture
:sprint: S07-S08 (Semaines 7-8)
:assigned: Étudiant Java OCP

[cols="1,2", options="header"]
|===
| Propriété | Valeur
| ID        | US009
| Titre     | Maîtriser polymorphisme et liaison dynamique
| Epic      | {epic}
| Sprint    | {sprint}
| Priorité  | {priority}
| Effort    | {effort}
| Statut    | {status}
|===

== 🎯 Description
En tant que développeur Java
Je veux maîtriser le polymorphisme et la liaison dynamique
Afin d'écrire du code flexible et extensible.

== ✅ Critères d'Acceptation
* [ ] Polymorphisme d'héritage - Méthodes redéfinies correctement
* [ ] Polymorphisme d'interface - Implémentations multiples
* [ ] Casting et instanceof - Vérifications de type sécurisées
* [ ] Collections polymorphes - Listes d'objets différents

== 🛠️ Tâches Principales

=== T031: Polymorphisme en Action (1h30)
[source,java]
----
public void demonstratePolymorphism() {
    List<Animal> animals = Arrays.asList(
        new Lion("Simba", 5, "King"),
        new Eagle("Aquila", 3),
        new Elephant("Dumbo", 10)
    );

    for (Animal animal : animals) {
        animal.makeSound();  // Liaison dynamique
        animal.move();       // Comportement spécifique

        // Polymorphisme d'interface
        if (animal instanceof Flyable) {
            ((Flyable) animal).fly();
        }
    }
}
----

Validation :
* [ ] Comportements différents selon le type réel
* [ ] Liaison dynamique fonctionnelle
* [ ] Collections polymorphes utilisées

=== T032: Interfaces et Implémentations (1h)
[source,java]
----
public interface Trainable {
    void train();
    boolean isTrainable();
}

public class Dog extends Animal implements Trainable {
    @Override
    public void train() {
        System.out.println(name + " is learning new tricks!");
    }

    @Override
    public boolean isTrainable() {
        return true;
    }
}

// Usage polymorphe
List<Trainable> trainableAnimals = getTrainableAnimals();
trainableAnimals.forEach(Trainable::train);
----

Validation :
* [ ] Interfaces utilisées polymorphiquement
* [ ] Implémentations multiples
* [ ] Méthodes d'interface appelées

=== T033: Casting Sécurisé (30 min)
[source,java]
----
public void handleSpecialBehavior(Animal animal) {
    if (animal instanceof Flyable flyingAnimal) {  // Pattern matching Java 17
        flyingAnimal.fly();
    }

    if (animal instanceof Mammal mammal) {
        mammal.nurse();
    }
}
----

Validation :
* [ ] Pas de ClassCastException
* [ ] instanceof utilisé correctement
* [ ] Pattern matching Java 17

== 📚 Ressources d'Apprentissage

=== 🎯 Ressources Principales (À étudier dans l'ordre)

==== **1. Concepts Polymorphisme Fondamentaux (2h30 d'étude)**
* **Oracle Polymorphism Tutorial** ⭐⭐⭐⭐⭐
  - Lien : https://docs.oracle.com/javase/tutorial/java/IandI/polymorphism.html
  - Concepts : Override, dynamic binding, interfaces
  - Points forts : Documentation officielle avec exemples
* **OpenClassrooms - Polymorphisme Java** ⭐⭐⭐⭐⭐
  - Lien : https://openclassrooms.com/fr/courses/8383791-apprenez-a-programmer-en-java
  - Chapitre polymorphisme avec exercices

==== **2. Interfaces et Classes Abstraites (2h d'apprentissage)**
* **Baeldung Polymorphism Guide** ⭐⭐⭐⭐⭐
  - Lien : https://www.baeldung.com/java-polymorphism
  - Interface vs Abstract class
  - Exemples concrets avec code
* **Java Brains - Polymorphism Videos** ⭐⭐⭐⭐⭐
  - Série complète sur polymorphisme
  - Visualisations et explications claires

==== **3. Design Patterns avec Polymorphisme (1h30 d'apprentissage)**
* **Refactoring Guru - Strategy Pattern** ⭐⭐⭐⭐⭐
  - Lien : https://refactoring.guru/design-patterns/strategy
  - Exemples interactifs avec polymorphisme
* **Head First Design Patterns** ⭐⭐⭐⭐⭐
  - Strategy Pattern avec polymorphisme
  - Disponible en bibliothèque

=== 📖 Ressources Approfondissement

==== **Concepts Avancés**
* **Dynamic Binding Explained** ⭐⭐⭐⭐
  - Mécanisme de liaison dynamique
  - Virtual method table
* **Interface Segregation Principle** ⭐⭐⭐⭐
  - SOLID principles avec polymorphisme

==== **Exercices Pratiques**
* **Polymorphism Katas** ⭐⭐⭐⭐
  - Exercices progressifs
  - Refactoring vers polymorphisme

=== 🔧 Outils de Validation

==== **IDE et Debugging**
* **VS Code Java** ⭐⭐⭐⭐⭐
  - Visualisation hiérarchie classes
  - Debugging méthodes virtuelles
* **UML Class Diagrams** ⭐⭐⭐⭐
  - Visualisation polymorphisme

==== **Aide Communautaire**
* **Stack Overflow** ⭐⭐⭐⭐⭐
  - Tags : [java] [polymorphism] [interface] [abstract-class]

== ⏱️ Estimation Temporelle Détaillée

=== 📊 Répartition Réaliste

[cols="4,2,2,1", options="header"]
|===
| Activité | Temps Standard | Temps Slower | Difficulté

| **📖 Apprentissage Théorique** | | |
| Concepts polymorphisme fondamentaux | 2h30 | 4h | 🔴 Difficile
| Interfaces et classes abstraites | 2h | 3h | 🔴 Difficile
| Design patterns | 1h30 | 2h | 🟡 Moyen

| **💻 Pratique Guidée** | | |
| Baeldung exercises | 2h | 3h | 🔴 Difficile
| Polymorphism katas | 1h30 | 2h | 🟡 Moyen

| **🛠️ Implémentation Projet** | | |
| T031: Polymorphisme en action | 1h30 | 2h30 | 🔴 Difficile
| T032: Interfaces et implémentations | 1h | 1h30 | 🟡 Moyen
| T033: Casting sécurisé | 30min | 1h | 🟡 Moyen

| **🧪 Debugging et Révisions** | | |
| Debugging polymorphisme | 1h30 | 2h30 | 🔴 Difficile
| Révisions concepts | 1h | 1h30 | 🟡 Moyen

| **🎯 TOTAL RÉALISTE** | **14h** | **22h** | **🔴 Difficile**
|===

[IMPORTANT]
====
**Polymorphisme = Concept Avancé POO !**

- **Estimation originale :** 3h (largement sous-estimée)
- **Estimation réaliste :** 14h standard / 22h slower
- **Pourquoi si long :**
  * Concept abstrait difficile à visualiser
  * Liaison dynamique complexe à comprendre
  * Debugging polymorphe délicat
  * Nécessite maîtrise héritage + interfaces
- **Recommandation :** Prévoir 2-3 semaines complètes
====

=== 🎯 Mapping Ressources → Tâches Spécifiques

==== **Préparation Intensive (6h d'apprentissage)**
1. **Oracle Polymorphism Tutorial** - Concepts fondamentaux
2. **Baeldung Polymorphism Guide** - Interface vs Abstract
3. **Java Brains Videos** - Visualisations claires
4. **Strategy Pattern** - Design pattern avec polymorphisme
5. **Validation :** Concepts polymorphisme maîtrisés

==== **Pour T031: Polymorphisme en Action**
1. **Dynamic Binding Explained** - Mécanisme liaison
2. **Collections polymorphes** - Listes d'objets différents
3. **UML Class Diagrams** - Visualisation hiérarchie
4. **Validation :** Liaison dynamique fonctionnelle

==== **Pour T032: Interfaces et Implémentations**
1. **Interface Segregation** - SOLID principles
2. **Multiple implementations** - Patterns courants
3. **Polymorphism Katas** - Exercices pratiques
4. **Validation :** Interfaces utilisées polymorphiquement

==== **Pour T033: Casting Sécurisé**
1. **instanceof patterns** - Vérifications sécurisées
2. **Pattern matching Java 17** - Syntaxe moderne
3. **ClassCastException prevention** - Bonnes pratiques
4. **Validation :** Pas d'exceptions runtime

== 🎯 Livrable : Polymorphisme dans virtual-zoo

== 🔗 Liens Connexes
* US008 - Héritage (prérequis)
* US010 - Collections List (suivante)
* Projet : 04-virtual-zoo
