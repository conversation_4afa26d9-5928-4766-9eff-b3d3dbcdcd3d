= US005 - Écrire Tests Unitaires Efficaces
:status: Backlog
:priority: Must Have
:effort: 3 points
:epic: 🚀 Fondations Solides Java
:sprint: S03-S04 (Semaines 3-4)
:assigned: Étudiant Java OCP

[cols="1,2", options="header"]
|===
| Propriété | Valeur
| ID        | US005
| Titre     | Écrire tests unitaires efficaces et maintenables
| Epic      | {epic}
| Sprint    | {sprint}
| Priorité  | {priority}
| Effort    | {effort}
| Statut    | {status}
| Dépendances | US004 (Debugging & TDD)
|===

== 🎯 Description
En tant que développeur Java
Je veux maîtriser l'écriture de tests unitaires efficaces
Afin de garantir la qualité de mon code, faciliter la maintenance et prévenir les régressions.

== ✅ Critères d'Acceptation

=== Qualité des Tests
* [ ] Tests lisibles - Noms expressifs et structure AAA (Arrange-Act-Assert)
* [ ] Tests isolés - Chaque test indépendant des autres
* [ ] Tests rapides - Exécution < 1 seconde par test
* [ ] Tests déterministes - Résultats reproductibles

=== Couverture et Organisation
* [ ] Couverture significative - 85%+ sur la logique métier
* [ ] Cas limites - Valeurs nulles, vides, extrêmes testées
* [ ] Tests d'erreurs - Exceptions et cas d'échec couverts
* [ ] Organisation logique - Classes de test bien structurées

=== Bonnes Pratiques
* [ ] Assertions appropriées - Types d'assertions adaptés au contexte
* [ ] Messages d'erreur - Descriptions claires en cas d'échec
* [ ] Setup/Teardown - Préparation et nettoyage efficaces
* [ ] Mocking basique - Isolation des dépendances

=== Intégration Continue
* [ ] Tests automatisés - Exécution via Maven
* [ ] Rapports de couverture - JaCoCo configuré et fonctionnel
* [ ] Échec de build - Tests cassés bloquent la compilation
* [ ] Métriques visibles - Badges et rapports accessibles

== 🛠️ Tâches Détaillées

=== T018: Principes des Tests Unitaires (1h)
Caractéristiques FIRST :
* **F**ast - Rapides à exécuter
* **I**ndependent - Indépendants les uns des autres
* **R**epeatable - Reproductibles dans tout environnement
* **S**elf-validating - Résultat binaire (pass/fail)
* **T**imely - Écrits au bon moment (TDD)

Structure AAA (Arrange-Act-Assert) :
[source,java]
----
@Test
void shouldCalculateCorrectTotalWithTax() {
    // Arrange - Préparer les données
    Calculator calculator = new Calculator();
    double price = 100.0;
    double taxRate = 0.20;

    // Act - Exécuter l'action
    double total = calculator.calculateTotalWithTax(price, taxRate);

    // Assert - Vérifier le résultat
    assertEquals(120.0, total, 0.01);
}
----

Nommage expressif :
[source,java]
----
// ❌ Mauvais
@Test void test1() { }
@Test void testAdd() { }

// ✅ Bon
@Test void shouldReturnSumWhenAddingTwoPositiveNumbers() { }
@Test void shouldThrowExceptionWhenDividingByZero() { }
@Test void shouldReturnEmptyListWhenNoItemsMatch() { }
----

Validation :
* [ ] Structure AAA appliquée dans tous les tests
* [ ] Noms de tests expressifs et descriptifs
* [ ] Tests indépendants et reproductibles
* [ ] Temps d'exécution < 1 seconde par test

=== T019: Assertions et Vérifications Avancées (1h)
[source,java]
----
// Égalité et comparaisons
assertEquals(expected, actual);
assertEquals(expected, actual, delta); // Pour les doubles
assertNotEquals(unexpected, actual);

// Conditions booléennes
assertTrue(condition);
assertFalse(condition);

// Nullité
assertNull(value);
assertNotNull(value);

// Collections
assertIterableEquals(expectedList, actualList);
assertArrayEquals(expectedArray, actualArray);

// Exceptions
assertThrows(IllegalArgumentException.class, () -> {
    calculator.divide(10, 0);
});

// Assertions groupées
assertAll("Person validation",
    () -> assertEquals("John", person.getName()),
    () -> assertEquals(25, person.getAge()),
    () -> assertTrue(person.isActive())
);

// Timeout
assertTimeout(Duration.ofSeconds(2), () -> {
    // Code qui doit s'exécuter rapidement
});
----

Messages d'erreur personnalisés :
[source,java]
----
assertEquals(expected, actual,
    "Le calcul de la TVA devrait retourner " + expected + " mais a retourné " + actual);

assertTrue(account.getBalance() > 0,
    () -> "Le solde du compte devrait être positif mais était: " + account.getBalance());
----

Validation :
* [ ] 8+ types d'assertions différentes utilisées
* [ ] Messages d'erreur personnalisés ajoutés
* [ ] Tests de collections implémentés
* [ ] Assertions groupées utilisées appropriément

=== T020: Organisation et Structure des Tests (1h)
[source,java]
----
class CalculatorTest {

    private Calculator calculator;

    @BeforeEach
    void setUp() {
        calculator = new Calculator();
    }

    @Nested
    @DisplayName("Addition operations")
    class AdditionOperations {

        @Test
        @DisplayName("Should return sum of two positive numbers")
        void shouldReturnSumOfTwoPositiveNumbers() {
            assertEquals(5, calculator.add(2, 3));
        }

        @Test
        @DisplayName("Should handle negative numbers correctly")
        void shouldHandleNegativeNumbers() {
            assertEquals(-1, calculator.add(-3, 2));
        }
    }

    @Nested
    @DisplayName("Division operations")
    class DivisionOperations {

        @Test
        void shouldThrowExceptionWhenDividingByZero() {
            assertThrows(ArithmeticException.class,
                () -> calculator.divide(10, 0));
        }
    }
}
----

Tests paramétrés :
[source,java]
----
@ParameterizedTest
@CsvSource({
    "2, 3, 5",
    "10, 15, 25",
    "-5, 5, 0",
    "0, 0, 0"
})
void shouldAddNumbersCorrectly(int a, int b, int expected) {
    assertEquals(expected, calculator.add(a, b));
}

@ParameterizedTest
@ValueSource(strings = {"", " ", "   "})
void shouldRejectEmptyOrBlankNames(String name) {
    assertThrows(IllegalArgumentException.class,
        () -> new Person(name));
}
----

Validation :
* [ ] Classes de test organisées avec @Nested
* [ ] Tests paramétrés utilisés pour éviter duplication
* [ ] @DisplayName utilisé pour clarifier les intentions
* [ ] JaCoCo configuré et rapports générés

=== T021: Couverture de Code et Métriques (30 min)
Configuration JaCoCo dans `pom.xml` :
[source,xml]
----
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <version>0.8.8</version>
    <configuration>
        <rules>
            <rule>
                <element>CLASS</element>
                <limits>
                    <limit>
                        <counter>LINE</counter>
                        <value>COVEREDRATIO</value>
                        <minimum>0.85</minimum>
                    </limit>
                </limits>
            </rule>
        </rules>
    </configuration>
</plugin>
----

Commandes Maven pour tests :
[source,bash]
----
# Exécuter tous les tests
mvn test

# Générer rapport de couverture
mvn jacoco:report

# Vérifier seuils de couverture
mvn jacoco:check

# Ouvrir rapport HTML
open target/site/jacoco/index.html
----

Métriques à surveiller :
* **Line Coverage** - % de lignes exécutées
* **Branch Coverage** - % de branches conditionnelles testées
* **Method Coverage** - % de méthodes appelées
* **Class Coverage** - % de classes utilisées

Validation :
* [ ] Couverture de ligne > 85%
* [ ] Couverture de branche > 80%
* [ ] Rapport JaCoCo généré automatiquement
* [ ] Métriques intégrées dans README

== 📚 Ressources d'Apprentissage

=== 🎯 Ressources Principales (À étudier dans l'ordre)

==== **1. Concepts Testing Fondamentaux (2h d'étude)**
* **JUnit 5 User Guide** ⭐⭐⭐⭐⭐
  - Lien : https://junit.org/junit5/docs/current/user-guide/
  - Concepts : Annotations, assertions, lifecycle
  - Points forts : Documentation officielle complète
* **Baeldung Testing Guide** ⭐⭐⭐⭐⭐
  - Lien : https://www.baeldung.com/junit-5
  - Guide complet JUnit 5 avec exemples
  - Progression step-by-step

==== **2. Assertions Avancées (1h30 d'apprentissage)**
* **AssertJ Documentation** ⭐⭐⭐⭐⭐
  - Lien : https://assertj.github.io/doc/
  - Assertions fluides et lisibles
  - Exemples pratiques nombreux
* **OpenClassrooms - Tests Java** ⭐⭐⭐⭐⭐
  - Lien : https://openclassrooms.com/fr/courses/6100311-testez-votre-code-java-pour-realiser-des-applications-de-qualite
  - Cours complet en français

==== **3. Coverage et Métriques (1h d'apprentissage)**
* **JaCoCo Documentation** ⭐⭐⭐⭐⭐
  - Lien : https://www.jacoco.org/jacoco/trunk/doc/
  - Configuration et utilisation
  - Métriques de couverture
* **SonarQube Quality Gates** ⭐⭐⭐⭐
  - Métriques qualité code
  - Standards industriels

=== 📖 Ressources Approfondissement

==== **Testing Patterns**
* **Effective Unit Testing - Livre Manning** ⭐⭐⭐⭐⭐
  - Patterns de tests efficaces
  - Organisation des tests
* **Mockito Framework** ⭐⭐⭐⭐
  - Lien : https://javadoc.io/doc/org.mockito/mockito-core/latest/org/mockito/Mockito.html
  - Mocking pour tests unitaires

=== 🔧 Outils et Configuration

==== **IDE Integration**
* **VS Code Java Test Runner** ⭐⭐⭐⭐⭐
  - Extension pour exécuter tests
  - Visualisation résultats
* **Maven Surefire Plugin** ⭐⭐⭐⭐⭐
  - Configuration tests Maven
  - Rapports automatiques

==== **Aide Communautaire**
* **Stack Overflow** ⭐⭐⭐⭐⭐
  - Tags : [java] [junit] [testing] [assertj]

== ⏱️ Estimation Temporelle
[cols="3,2,1", options="header"]
|===
| Tâche | Durée | Difficulté
| Principes tests | 1h | 🟢 Facile
| Assertions avancées | 1h | 🟡 Moyen
| Organisation tests | 1h | 🟡 Moyen
| Couverture et métriques | 30 min | 🟢 Facile
| **Total** | **3h30** | **🟡 Moyen**
|===

== 📊 Métriques de Succès
* Couverture ligne > 85%
* Couverture branche > 80%
* Temps d'exécution < 30 secondes pour tous les tests
* Nombre de tests > 50 pour le projet

== 🔗 Liens Connexes
* US004 - Debugging & TDD (prérequis)
* US006 - Classes et Objets (suivante)
* Projet : 02-guessing-game-tdd
* Templates : JUnit 5 Test Setup
