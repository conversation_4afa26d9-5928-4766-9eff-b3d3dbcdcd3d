= US011 - Utiliser HashMap et TreeMap
:status: Backlog
:priority: Must Have
:effort: 5 points
:epic: 📚 Collections Fundamentales
:sprint: S09-S11 (Semaines 9-11)
:assigned: Étudiant Java OCP

[cols="1,2", options="header"]
|===
| Propriété | Valeur
| ID        | US011
| Titre     | Utiliser HashMap et TreeMap efficacement
| Epic      | {epic}
| Sprint    | {sprint}
| Priorité  | {priority}
| Effort    | {effort}
| Statut    | {status}
|===

== 🎯 Description
En tant que développeur Java
Je veux maîtriser les Maps (HashMap, TreeMap)
Afin de gérer efficacement des associations clé-valeur.

== ✅ Critères d'Acceptation
* [ ] HashMap maîtrisé - put, get, containsKey, performance O(1)
* [ ] TreeMap compris - Tri automatique, navigation
* [ ] Itération Maps - keySet, values, entrySet
* [ ] Index contacts - Recherche rapide par différents critères

== 🛠️ Tâches Principales

=== T038: HashMap Operations (2h)
[source,java]
----
public class ContactIndex {
    private Map<String, Contact> byEmail = new HashMap<>();
    private Map<String, List<Contact>> byCity = new HashMap<>();

    public void addContact(Contact contact) {
        byEmail.put(contact.getEmail(), contact);

        byCity.computeIfAbsent(contact.getCity(), k -> new ArrayList<>())
              .add(contact);
    }

    public Contact findByEmail(String email) {
        return byEmail.get(email);  // O(1)
    }

    public List<Contact> findByCity(String city) {
        return byCity.getOrDefault(city, Collections.emptyList());
    }
}
----

Validation :
* [ ] Index multi-critères fonctionnel
* [ ] Performance O(1) comprise
* [ ] Méthodes HashMap maîtrisées

=== T039: TreeMap et Tri (1h30)
[source,java]
----
public class SortedContactManager {
    private TreeMap<String, Contact> contactsByName = new TreeMap<>();

    public void addContact(Contact contact) {
        contactsByName.put(contact.getName(), contact);
    }

    public List<Contact> getContactsInRange(String from, String to) {
        return new ArrayList<>(
            contactsByName.subMap(from, to).values()
        );
    }

    public Contact getFirstContact() {
        return contactsByName.firstEntry().getValue();
    }
}
----

Validation :
* [ ] Tri automatique fonctionnel
* [ ] Navigation TreeMap maîtrisée
* [ ] Méthodes spécialisées utilisées

=== T040: Itération et Performance (1h)
[source,java]
----
// Itérer sur les entrées (plus efficace)
for (Map.Entry<String, Contact> entry : contacts.entrySet()) {
    String email = entry.getKey();
    Contact contact = entry.getValue();
    // Traitement
}

// Avec streams
contacts.entrySet().stream()
    .filter(entry -> entry.getValue().isActive())
    .forEach(entry -> System.out.println(entry.getKey()));
----

Validation :
* [ ] Toutes les techniques d'itération maîtrisées
* [ ] Performance optimisée
* [ ] Streams avec Maps

=== T041: Système d'Index Complet (30 min)
Intégrer Maps dans l'application pour recherches ultra-rapides.

Validation :
* [ ] Recherches rapides et tri automatique
* [ ] Index multi-critères fonctionnel

== 📚 Ressources d'Apprentissage

=== 🎯 Ressources Principales (À étudier dans l'ordre)

==== **1. Map Interface Fondamentaux (2h d'étude)**
* **Oracle Map Interface Tutorial** ⭐⭐⭐⭐⭐
  - Lien : https://docs.oracle.com/javase/tutorial/collections/interfaces/map.html
  - Concepts : Map, HashMap, TreeMap, LinkedHashMap
  - Points forts : Documentation officielle complète
* **OpenClassrooms - Maps Java** ⭐⭐⭐⭐⭐
  - Lien : https://openclassrooms.com/fr/courses/8383791-apprenez-a-programmer-en-java
  - Chapitre maps avec exercices

==== **2. HashMap vs TreeMap (1h30 d'apprentissage)**
* **Baeldung HashMap Guide** ⭐⭐⭐⭐⭐
  - Lien : https://www.baeldung.com/java-hashmap
  - Fonctionnement interne, performance
* **Baeldung TreeMap Guide** ⭐⭐⭐⭐⭐
  - Lien : https://www.baeldung.com/java-treemap
  - Navigation, tri automatique

==== **3. Performance et Choix (1h d'apprentissage)**
* **Map Performance Comparison** ⭐⭐⭐⭐
  - HashMap vs TreeMap vs LinkedHashMap
  - Cas d'usage appropriés

=== 📖 Ressources Approfondissement

==== **Concepts Avancés**
* **HashMap Internals** ⭐⭐⭐⭐
  - Buckets, collisions, resize
* **Map Manipulation Katas** ⭐⭐⭐⭐
  - Exercices progressifs

=== 🔧 Outils de Validation

==== **IDE et Debugging**
* **VS Code Java** ⭐⭐⭐⭐⭐
  - Visualisation contenu maps

==== **Aide Communautaire**
* **Stack Overflow** ⭐⭐⭐⭐⭐
  - Tags : [java] [hashmap] [treemap] [map]

== ⏱️ Estimation Temporelle Détaillée

=== 📊 Répartition Réaliste

[cols="4,2,2,1", options="header"]
|===
| Activité | Temps Standard | Temps Slower | Difficulté

| **📖 Apprentissage Théorique** | | |
| Map interface fondamentaux | 2h | 3h | 🟡 Moyen
| HashMap vs TreeMap | 1h30 | 2h | 🟡 Moyen
| Performance et choix | 1h | 1h30 | 🟡 Moyen

| **💻 Pratique Guidée** | | |
| Baeldung exercises | 1h30 | 2h | 🟡 Moyen
| Map manipulation katas | 1h | 1h30 | 🟡 Moyen

| **🛠️ Implémentation Projet** | | |
| T037: Index par nom | 1h | 1h30 | 🟡 Moyen
| T038: Index par ville | 1h | 1h30 | 🟡 Moyen
| T039: Index multi-critères | 1h30 | 2h | 🟡 Moyen

| **🧪 Tests et Validation** | | |
| Tests unitaires maps | 1h | 1h30 | 🟡 Moyen
| Tests performance | 30min | 1h | 🟡 Moyen

| **🎯 TOTAL RÉALISTE** | **11h** | **16h** | **🟡 Moyen**
|===

[WARNING]
====
**Maps = Structures Clé-Valeur Essentielles !**

- **Estimation originale :** 5h (sous-estimée)
- **Estimation réaliste :** 11h standard / 16h slower
- **Pourquoi plus long :**
  * Apprentissage différents types de maps
  * Compréhension HashMap internals
  * Maîtrise navigation TreeMap
  * Pratique patterns key-value
- **Recommandation :** Prévoir 2 semaines
====

=== 🎯 Mapping Ressources → Tâches Spécifiques

==== **Préparation (4h30 d'apprentissage)**
1. **Oracle Map Tutorial** - Interface complète
2. **Baeldung HashMap/TreeMap** - Comparaison détaillée
3. **Performance Analysis** - Choix approprié
4. **Map Katas** - Pratique manipulation
5. **Validation :** Map interface maîtrisée

==== **Pour T037-T039: Index Multi-Critères**
1. **HashMap operations** - Put, get, remove
2. **Key-value patterns** - Stockage efficace
3. **Iteration methods** - Parcours maps
4. **Validation :** Index fonctionnel et performant

== 🎯 Livrable : Index avancé dans address-book

== 🔗 Liens Connexes
* US010 - Collections List (prérequis)
* US012 - Algorithmes Tri (suivante)
* Projet : 05-address-book
