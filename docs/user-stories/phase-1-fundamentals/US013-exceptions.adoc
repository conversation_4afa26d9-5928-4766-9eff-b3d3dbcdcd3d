= US013 - Gérer Exceptions Proprement
:status: Backlog
:priority: Must Have
:effort: 5 points
:epic: ⚠️ Exceptions & I/O
:sprint: S12-S14 (Semaines 12-14)
:assigned: Étudiant Java OCP

[cols="1,2", options="header"]
|===
| Propriété | Valeur
| ID        | US013
| Titre     | Gérer exceptions et erreurs proprement
| Epic      | {epic}
| Sprint    | {sprint}
| Priorité  | {priority}
| Effort    | {effort}
| Statut    | {status}
|===

== 🎯 Description
En tant que développeur Java
Je veux maîtriser la gestion d'exceptions
Afin de créer des applications robustes qui gèrent gracieusement les erreurs.

== ✅ Critères d'Acceptation
* [ ] Try-catch-finally - Gestion d'exceptions basique
* [ ] Exceptions custom - Création d'exceptions métier
* [ ] Checked vs Unchecked - Différences comprises
* [ ] Application robuste - Gestion d'erreurs complète

== 🛠️ Tâches Principales

=== T045: Try-Catch-Finally (1h30)
[source,java]
----
public class TaskManager {
    
    public void saveTask(Task task) {
        FileWriter writer = null;
        try {
            writer = new FileWriter("tasks.txt", true);
            writer.write(task.toString() + "\n");
            System.out.println("Task saved successfully");
        } catch (IOException e) {
            System.err.println("Error saving task: " + e.getMessage());
            // Log l'erreur
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    System.err.println("Error closing file: " + e.getMessage());
                }
            }
        }
    }
}
----

Validation :
* [ ] Gestion d'erreurs sans crash application
* [ ] Finally block pour nettoyage
* [ ] Exceptions imbriquées gérées

=== T046: Exceptions Personnalisées (1h30)
[source,java]
----
// Exception checked pour erreurs métier
public class TaskNotFoundException extends Exception {
    public TaskNotFoundException(String message) {
        super(message);
    }
    
    public TaskNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}

// Exception unchecked pour erreurs de programmation
public class InvalidTaskStateException extends RuntimeException {
    public InvalidTaskStateException(String message) {
        super(message);
    }
}

// Usage
public Task findTask(int id) throws TaskNotFoundException {
    Task task = repository.findById(id);
    if (task == null) {
        throw new TaskNotFoundException("Task with ID " + id + " not found");
    }
    return task;
}
----

Validation :
* [ ] Exceptions métier appropriées
* [ ] Hiérarchie d'exceptions cohérente
* [ ] Messages d'erreur explicites

=== T047: Try-with-Resources (1h)
[source,java]
----
public class FileTaskManager {
    
    public List<Task> loadTasks() throws IOException {
        List<Task> tasks = new ArrayList<>();
        
        // Try-with-resources (Java 7+)
        try (BufferedReader reader = Files.newBufferedReader(
                Paths.get("tasks.txt"), StandardCharsets.UTF_8)) {
            
            String line;
            while ((line = reader.readLine()) != null) {
                try {
                    Task task = parseTask(line);
                    tasks.add(task);
                } catch (ParseException e) {
                    System.err.println("Skipping invalid task: " + line);
                    // Continue avec les autres tâches
                }
            }
        }
        return tasks;
    }
}
----

Validation :
* [ ] Ressources fermées automatiquement
* [ ] Try-with-resources maîtrisé
* [ ] Gestion d'erreurs partielles

=== T048: Stratégies de Gestion d'Erreurs (1h)
[source,java]
----
public class RobustTaskManager {
    
    // Pattern: Fail Fast
    public void addTask(Task task) {
        Objects.requireNonNull(task, "Task cannot be null");
        if (task.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("Task title cannot be empty");
        }
        // Continuer avec la logique
    }
    
    // Pattern: Graceful Degradation
    public List<Task> searchTasks(String query) {
        try {
            return searchService.search(query);
        } catch (SearchServiceException e) {
            logger.warn("Search service unavailable, using local search", e);
            return localSearch(query);  // Fallback
        }
    }
}
----

Validation :
* [ ] Patterns de robustesse appliqués
* [ ] Fail-fast vs graceful degradation
* [ ] Fallback mechanisms

== 📚 Ressources
* Oracle Exceptions - Tutorial officiel
* Baeldung Exceptions - Guide complet
* Effective Java - Chapitre Exceptions

== ⏱️ Estimation : 5h | 🎯 Livrable : 06-task-manager

== 🔗 Liens Connexes
* US012 - Algorithmes Tri (prérequis)
* US014 - Fichiers I/O (suivante)
* Projet : 06-task-manager
