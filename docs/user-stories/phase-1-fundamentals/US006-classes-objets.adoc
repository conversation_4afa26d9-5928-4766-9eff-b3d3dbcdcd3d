= US006 - Concevoir Classes et Objets
:status: Backlog
:priority: Must Have
:effort: 5 points
:epic: 🏗️ Maîtrise POO & Architecture
:sprint: S05-S06 (Semaines 5-6)
:assigned: Étudiant Java OCP

[cols="1,2", options="header"]
|===
| Propriété | Valeur
| ID        | US006
| Titre     | Concevoir classes et objets avec POO
| Epic      | {epic}
| Sprint    | {sprint}
| Priorité  | {priority}
| Effort    | {effort}
| Statut    | {status}
|===

== 🎯 Description
En tant qu'étudiant Java
Je veux maîtriser la création de classes et objets
Afin de pouvoir modéliser des problèmes réels avec la programmation orientée objet.

== ✅ Critères d'Acceptation

=== Classes Bien Conçues
* [ ] Attributs privés avec encapsulation appropriée
* [ ] Constructeurs avec validation des paramètres
* [ ] Méthodes avec responsabilités claires
* [ ] Conventions de nommage respectées

=== Objets Fonctionnels
* [ ] Instanciation correcte avec `new`
* [ ] Utilisation des getters/setters appropriés
* [ ] Méthodes d'instance vs méthodes statiques
* [ ] Cycle de vie des objets compris

=== Encapsulation Basique
* [ ] Attributs privés protégés
* [ ] Getters/setters avec validation
* [ ] Méthodes publiques comme interface
* [ ] Détails d'implémentation cachés

=== Projet Bancaire
* [ ] Système complet avec comptes et transactions
* [ ] Classes Account, Customer, Transaction
* [ ] Opérations bancaires fonctionnelles
* [ ] Tests unitaires complets

== 🛠️ Tâches Détaillées

=== T022: Conception de Classes (2h)
[source,java]
----
public class Account {
    private String accountNumber;
    private double balance;
    private Customer owner;
    private List<Transaction> transactions;

    public Account(String accountNumber, Customer owner) {
        if (accountNumber == null || accountNumber.trim().isEmpty()) {
            throw new IllegalArgumentException("Account number cannot be null or empty");
        }
        if (owner == null) {
            throw new IllegalArgumentException("Owner cannot be null");
        }

        this.accountNumber = accountNumber;
        this.owner = owner;
        this.balance = 0.0;
        this.transactions = new ArrayList<>();
    }

    public void deposit(double amount) {
        if (amount <= 0) {
            throw new IllegalArgumentException("Deposit amount must be positive");
        }
        this.balance += amount;
        this.transactions.add(new Transaction(TransactionType.DEPOSIT, amount));
    }

    public boolean withdraw(double amount) {
        if (amount <= 0) {
            throw new IllegalArgumentException("Withdrawal amount must be positive");
        }
        if (amount > balance) {
            return false; // Insufficient funds
        }
        this.balance -= amount;
        this.transactions.add(new Transaction(TransactionType.WITHDRAWAL, amount));
        return true;
    }

    // Getters
    public String getAccountNumber() { return accountNumber; }
    public double getBalance() { return balance; }
    public Customer getOwner() { return owner; }
    public List<Transaction> getTransactions() {
        return new ArrayList<>(transactions); // Defensive copy
    }
}
----

[source,java]
----
public class Customer {
    private int customerId;
    private String name;
    private String email;
    private LocalDate dateOfBirth;

    public Customer(int customerId, String name, String email, LocalDate dateOfBirth) {
        validateCustomerId(customerId);
        validateName(name);
        validateEmail(email);
        validateDateOfBirth(dateOfBirth);

        this.customerId = customerId;
        this.name = name;
        this.email = email;
        this.dateOfBirth = dateOfBirth;
    }

    private void validateCustomerId(int customerId) {
        if (customerId <= 0) {
            throw new IllegalArgumentException("Customer ID must be positive");
        }
    }

    private void validateName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Name cannot be null or empty");
        }
    }

    // Autres validations...
}
----

Validation :
* [ ] Classes compilent sans erreurs
* [ ] Constructeurs avec validation
* [ ] Méthodes avec logique métier
* [ ] Encapsulation respectée

=== T023: Gestion des Objets (1h30)
[source,java]
----
public class BankingSystemDemo {
    public static void main(String[] args) {
        // Créer un client
        Customer john = new Customer(1, "John Doe", "<EMAIL>",
                                   LocalDate.of(1990, 5, 15));

        // Créer un compte
        Account account = new Account("ACC001", john);

        // Opérations bancaires
        account.deposit(1000.0);
        System.out.println("Balance after deposit: " + account.getBalance());

        boolean withdrawalSuccess = account.withdraw(200.0);
        if (withdrawalSuccess) {
            System.out.println("Withdrawal successful. New balance: " + account.getBalance());
        }

        // Tentative de retrait avec fonds insuffisants
        boolean largeWithdrawal = account.withdraw(1500.0);
        if (!largeWithdrawal) {
            System.out.println("Insufficient funds for withdrawal");
        }

        // Afficher l'historique des transactions
        System.out.println("Transaction history:");
        for (Transaction transaction : account.getTransactions()) {
            System.out.println(transaction);
        }
    }
}
----

[source,java]
----
public class Transaction {
    private final TransactionType type;
    private final double amount;
    private final LocalDateTime timestamp;
    private final String description;

    public Transaction(TransactionType type, double amount) {
        this(type, amount, type.getDefaultDescription());
    }

    public Transaction(TransactionType type, double amount, String description) {
        this.type = type;
        this.amount = amount;
        this.timestamp = LocalDateTime.now();
        this.description = description;
    }

    @Override
    public String toString() {
        return String.format("%s: %.2f - %s (%s)",
                           type, amount, description, timestamp.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
    }
}

enum TransactionType {
    DEPOSIT("Deposit"),
    WITHDRAWAL("Withdrawal"),
    TRANSFER("Transfer");

    private final String defaultDescription;

    TransactionType(String defaultDescription) {
        this.defaultDescription = defaultDescription;
    }

    public String getDefaultDescription() {
        return defaultDescription;
    }
}
----

Validation :
* [ ] Objets créés et utilisés correctement
* [ ] Opérations bancaires fonctionnelles
* [ ] Gestion des cas d'erreur
* [ ] Historique des transactions

=== T024: Tests et Validation (1h30)
[source,java]
----
class AccountTest {
    private Customer testCustomer;
    private Account testAccount;

    @BeforeEach
    void setUp() {
        testCustomer = new Customer(1, "Test User", "<EMAIL>",
                                  LocalDate.of(1990, 1, 1));
        testAccount = new Account("TEST001", testCustomer);
    }

    @Test
    void shouldCreateAccountWithZeroBalance() {
        assertEquals(0.0, testAccount.getBalance());
        assertEquals("TEST001", testAccount.getAccountNumber());
        assertEquals(testCustomer, testAccount.getOwner());
    }

    @Test
    void shouldDepositMoneySuccessfully() {
        testAccount.deposit(100.0);
        assertEquals(100.0, testAccount.getBalance());
        assertEquals(1, testAccount.getTransactions().size());
    }

    @Test
    void shouldWithdrawMoneyWhenSufficientFunds() {
        testAccount.deposit(200.0);
        boolean result = testAccount.withdraw(50.0);

        assertTrue(result);
        assertEquals(150.0, testAccount.getBalance());
        assertEquals(2, testAccount.getTransactions().size());
    }

    @Test
    void shouldRejectWithdrawalWhenInsufficientFunds() {
        testAccount.deposit(100.0);
        boolean result = testAccount.withdraw(150.0);

        assertFalse(result);
        assertEquals(100.0, testAccount.getBalance());
        assertEquals(1, testAccount.getTransactions().size()); // Only deposit
    }

    @Test
    void shouldThrowExceptionForNegativeDeposit() {
        assertThrows(IllegalArgumentException.class, () -> {
            testAccount.deposit(-50.0);
        });
    }

    @Test
    void shouldThrowExceptionForInvalidAccountNumber() {
        assertThrows(IllegalArgumentException.class, () -> {
            new Account("", testCustomer);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            new Account(null, testCustomer);
        });
    }
}
----

Validation :
* [ ] Couverture > 85% sur toutes les classes
* [ ] Tous les tests passent
* [ ] Tests des cas d'erreur
* [ ] Tests des cas limites

== 📚 Ressources d'Apprentissage

=== 🎯 Ressources Principales (À étudier dans l'ordre)

==== **1. Concepts POO Fondamentaux (4h d'étude)**
* **Oracle OOP Tutorial** ⭐⭐⭐⭐⭐
  - Lien : https://docs.oracle.com/javase/tutorial/java/concepts/
  - Concepts : Classes, objets, méthodes, attributs
  - Points forts : Documentation officielle, exemples progressifs
* **OpenClassrooms - POO en Java** ⭐⭐⭐⭐⭐
  - Lien : https://openclassrooms.com/fr/courses/8383791-apprenez-a-programmer-en-java
  - Chapitres 5-7 : Classes, objets, constructeurs
  - Exercices interactifs et quiz

==== **2. Vidéos Explicatives POO (2h30 de visionnage)**
* **FreeCodeCamp - Java OOP Section** ⭐⭐⭐⭐⭐
  - Section POO du cours complet (3h-5h de la vidéo 14h)
  - Progression logique et exemples pratiques
* **Java Brains - OOP Concepts** ⭐⭐⭐⭐⭐
  - Série complète sur la POO en Java
  - Exemples concrets et visualisations

==== **3. Pratique Guidée (3h de coding)**
* **Baeldung OOP Guide** ⭐⭐⭐⭐⭐
  - Lien : https://www.baeldung.com/java-oop
  - Exemples step-by-step avec code complet
  - Patterns et bonnes pratiques
* **Oracle MyLearn - Modules 5-6** ⭐⭐⭐⭐⭐
  - Classes & Objects avec exercices pratiques
  - Certification Oracle incluse

=== 📖 Ressources Approfondissement

==== **Concepts Avancés**
* **Head First Java - Chapitres 1-4** ⭐⭐⭐⭐⭐
  - Approche visuelle et ludique de la POO
  - Analogies et métaphores efficaces
* **UML Class Diagrams** ⭐⭐⭐⭐
  - Lien : https://www.lucidchart.com/pages/uml-class-diagram
  - Visualisation des relations entre classes

==== **Exercices Pratiques**
* **CodingBat OOP Problems** ⭐⭐⭐⭐
  - Exercices spécifiques POO
* **HackerRank Java OOP** ⭐⭐⭐⭐
  - Lien : https://www.hackerrank.com/domains/java
  - Problèmes progressifs avec classes

=== 🎥 Ressources Visuelles

==== **Analogies et Métaphores**
* **POO expliquée simplement** ⭐⭐⭐⭐⭐
  - Voiture = Classe, Ma voiture = Objet
  - Moule à gâteau = Classe, Gâteau = Objet
  - Recette = Classe, Plat cuisiné = Objet

=== 🔧 Outils de Validation

==== **IDE et Debugging**
* **VS Code Java** ⭐⭐⭐⭐⭐
  - Visualisation des objets en mémoire
  - Debugging step-by-step des méthodes
* **Java Visualizer** ⭐⭐⭐⭐
  - Lien : http://www.pythontutor.com/java.html
  - Visualisation exécution code POO

==== **Aide Communautaire**
* **Stack Overflow** ⭐⭐⭐⭐⭐
  - Tags : [java] [oop] [class] [object]
* **Reddit r/learnjava** ⭐⭐⭐⭐
  - Questions conceptuelles POO bienvenues

== ⏱️ Estimation Temporelle Détaillée

=== 📊 Répartition Réaliste

[cols="4,2,2,1", options="header"]
|===
| Activité | Temps Standard | Temps Slower | Difficulté

| **📖 Apprentissage Théorique** | | |
| Concepts POO fondamentaux | 4h | 6h | 🔴 Difficile
| Vidéos explicatives POO | 2h30 | 4h | 🟡 Moyen
| UML et visualisation | 1h | 1h30 | 🟡 Moyen

| **💻 Pratique Guidée** | | |
| Baeldung OOP exercises | 3h | 5h | 🔴 Difficile
| Oracle MyLearn modules | 2h | 3h | 🟡 Moyen
| CodingBat OOP problems | 1h | 2h | 🟡 Moyen

| **🛠️ Implémentation Projet** | | |
| T018: Conception classes | 2h | 4h | 🔴 Difficile
| T019: Gestion objets | 1h30 | 3h | 🔴 Difficile
| T020: Tests et validation | 1h30 | 2h30 | 🟡 Moyen

| **🧪 Debugging et Révisions** | | |
| Debugging POO complexe | 2h | 4h | 🔴 Difficile
| Révisions concepts | 1h | 2h | 🟡 Moyen
| Refactoring et amélioration | 1h | 2h | 🟡 Moyen

| **🎯 TOTAL RÉALISTE** | **21h** | **35h** | **🔴 Très Difficile**
|===

[IMPORTANT]
====
**POO = Changement de Paradigme Majeur !**

- **Estimation originale :** 5h (largement sous-estimée)
- **Estimation réaliste :** 21h standard / 35h slower
- **Pourquoi si long :**
  * Passage de programmation procédurale à orientée objet
  * Concepts abstraits difficiles à visualiser
  * Debugging d'objets et méthodes complexe
  * Nécessite beaucoup de pratique pour assimiler
- **Recommandation :** Prévoir 3-5 semaines complètes
====

== 📊 Métriques de Succès
* Classes bien structurées avec responsabilités claires
* Encapsulation respectée (attributs privés)
* Tests unitaires avec couverture > 85%
* Code compilé sans warnings

== 🔗 Liens Connexes
* US005 - Tests Unitaires (prérequis)
* US007 - Encapsulation (suivante)
* US008 - Héritage (suivante)
* Projet : 03-banking-system
