= US012 - Implémenter Algorithmes de Tri
:status: Backlog
:priority: Should Have
:effort: 3 points
:epic: 📚 Collections Fundamentales
:sprint: S09-S11 (Semaines 9-11)
:assigned: Étudiant Java OCP

[cols="1,2", options="header"]
|===
| Propriété | Valeur
| ID        | US012
| Titre     | Implémenter algorithmes de tri et Comparator
| Epic      | {epic}
| Sprint    | {sprint}
| Priorité  | {priority}
| Effort    | {effort}
| Statut    | {status}
|===

== 🎯 Description
En tant que développeur Java
Je veux maîtriser les algorithmes de tri et Comparator
Afin d'organiser efficacement les données selon différents critères.

== ✅ Critères d'Acceptation
* [ ] Collections.sort() - Tri naturel et avec Comparator
* [ ] Comparator custom - Tri multi-critères
* [ ] Tri en place - Modification directe des listes
* [ ] Performance - Complexité temporelle comprise

== 🛠️ Tâches Principales

=== T042: Tri avec Collections.sort() (1h)
[source,java]
----
public class ContactSorter {
    
    public void sortByName(List<Contact> contacts) {
        Collections.sort(contacts, 
            Comparator.comparing(Contact::getName));
    }
    
    public void sortByAge(List<Contact> contacts) {
        contacts.sort(Comparator.comparing(Contact::getAge));
    }
    
    public void sortByNameThenAge(List<Contact> contacts) {
        contacts.sort(
            Comparator.comparing(Contact::getName)
                     .thenComparing(Contact::getAge)
        );
    }
}
----

Validation :
* [ ] Tri simple et multi-critères fonctionnels
* [ ] Comparator.comparing() maîtrisé
* [ ] Chaînage avec thenComparing()

=== T043: Comparator Personnalisés (1h)
[source,java]
----
public class CustomComparators {
    
    // Tri par longueur du nom
    public static final Comparator<Contact> BY_NAME_LENGTH = 
        Comparator.comparing(c -> c.getName().length());
    
    // Tri par domaine email
    public static final Comparator<Contact> BY_EMAIL_DOMAIN = 
        Comparator.comparing(c -> c.getEmail().split("@")[1]);
    
    // Tri complexe avec logique métier
    public static final Comparator<Contact> BY_PRIORITY = (c1, c2) -> {
        if (c1.isVip() && !c2.isVip()) return -1;
        if (!c1.isVip() && c2.isVip()) return 1;
        return c1.getName().compareTo(c2.getName());
    };
}
----

Validation :
* [ ] Comparators custom fonctionnels
* [ ] Logique métier dans tri
* [ ] Lambda expressions utilisées

=== T044: Tri et Performance (1h)
[source,java]
----
public void demonstrateSortingPerformance() {
    List<Contact> contacts = generateLargeContactList(100000);
    
    // Mesurer temps de tri
    long start = System.nanoTime();
    contacts.sort(Comparator.comparing(Contact::getName));
    long duration = System.nanoTime() - start;
    
    System.out.println("Tri de 100k contacts: " + duration/1_000_000 + "ms");
    
    // Comparaison avec TreeSet (tri automatique)
    Set<Contact> sortedSet = new TreeSet<>(
        Comparator.comparing(Contact::getName)
    );
    sortedSet.addAll(contacts);
}
----

Validation :
* [ ] Performance mesurée et comprise
* [ ] Comparaison algorithmes différents
* [ ] Complexité temporelle analysée

== 📚 Ressources
* Oracle Sorting - Algorithmes Collections
* Baeldung Comparator - Guide Comparator

== ⏱️ Estimation : 3h | 🎯 Livrable : Tri avancé dans address-book

== 🔗 Liens Connexes
* US011 - Collections Map (prérequis)
* US013 - Exceptions (suivante)
* Projet : 05-address-book
