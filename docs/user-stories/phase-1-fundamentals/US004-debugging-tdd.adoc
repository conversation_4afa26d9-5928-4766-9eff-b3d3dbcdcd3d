= US004 - Maîtriser Debugging et TDD
:status: Backlog
:priority: Must Have
:effort: 5 points
:epic: 🚀 Fondations Solides Java
:sprint: S03-S04 (Semaines 3-4)
:assigned: Étudiant Java OCP

[cols="1,2", options="header"]
|===
| Propriété | Valeur
| ID        | US004
| Titre     | Maîtriser debugging et Test-Driven Development
| Epic      | {epic}
| Sprint    | {sprint}
| Priorité  | {priority}
| Effort    | {effort}
| Statut    | {status}
| Dépendances | US002 (Syntaxe Java)
|===

== 🎯 Description
En tant qu'étudiant Java
Je veux maîtriser le debugging et l'approche Test-Driven Development
Afin de pouvoir identifier et corriger efficacement les bugs, et développer du code robuste dès le départ.

== ✅ Critères d'Acceptation

=== Debugging avec VS Code
* [ ] Breakpoints - Placement et utilisation efficace
* [ ] Step debugging - Step into, step over, step out maîtrisés
* [ ] Variables inspection - Visualisation des valeurs en temps réel
* [ ] Call stack - Compréhension du flux d'exécution

=== Test-Driven Development (TDD)
* [ ] Cycle Red-Green-Refactor - Méthodologie appliquée
* [ ] Tests avant code - Écriture des tests en premier
* [ ] Assertions JUnit - assertEquals, assertTrue, assertThrows
* [ ] Test coverage - Couverture de code > 85%

=== JUnit 5 Maîtrise
* [ ] Structure de test - @Test, @BeforeEach, @AfterEach
* [ ] Parameterized tests - @ParameterizedTest avec différentes entrées
* [ ] Test exceptions - Vérification des erreurs attendues
* [ ] Test organization - Classes de test bien structurées

=== Projet Pratique
* [ ] Jeu de devinette - Développé entièrement en TDD
* [ ] Tests complets - Tous les cas couverts
* [ ] Code robuste - Gestion d'erreurs et validation
* [ ] Documentation - Tests comme documentation vivante

== 🛠️ Tâches Détaillées

=== T014: Maîtriser Debugging VS Code (1h30)
Configuration debugging `.vscode/launch.json` :
[source,json]
----
{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "Debug Main",
            "request": "launch",
            "mainClass": "com.guessinggame.Main",
            "projectName": "guessing-game-tdd"
        },
        {
            "type": "java",
            "name": "Debug Tests",
            "request": "launch",
            "mainClass": "",
            "projectName": "guessing-game-tdd",
            "preLaunchTask": "java: run tests"
        }
    ]
}
----

Techniques de debugging :
1. **Breakpoints conditionnels** - S'arrêter seulement si condition vraie
2. **Logpoints** - Afficher valeurs sans arrêter l'exécution
3. **Exception breakpoints** - S'arrêter sur toutes les exceptions
4. **Watch expressions** - Surveiller expressions complexes

Exercices pratiques :
1. Déboguer calculatrice avec division par zéro
2. Tracer l'exécution d'une boucle avec breakpoints
3. Inspecter variables dans différents scopes
4. Utiliser call stack pour comprendre flux d'appels

Validation :
* [ ] Debugging session de 15+ minutes réussie
* [ ] Breakpoints utilisés efficacement
* [ ] Variables inspectées et comprises
* [ ] Bug identifié et corrigé via debugging

=== T015: Introduction au TDD (2h)
Cycle Red-Green-Refactor :
----
🔴 RED: Écrire un test qui échoue
🟢 GREEN: Écrire le minimum de code pour passer le test
🔵 REFACTOR: Améliorer le code sans casser les tests
----

Exemple concret - Classe NumberGuesser :
[source,java]
----
// 1. RED - Test qui échoue
@Test
void shouldReturnTooHighWhenGuessIsAboveTarget() {
    NumberGuesser game = new NumberGuesser(50);
    GuessResult result = game.guess(75);
    assertEquals(GuessResult.TOO_HIGH, result);
}

// 2. GREEN - Code minimal
public class NumberGuesser {
    private int target;

    public NumberGuesser(int target) {
        this.target = target;
    }

    public GuessResult guess(int guess) {
        if (guess > target) return GuessResult.TOO_HIGH;
        return GuessResult.CORRECT; // Temporaire
    }
}

// 3. REFACTOR - Améliorer après plus de tests
----

Avantages du TDD :
* **Design émergent** - L'API se dessine naturellement
* **Couverture garantie** - Chaque ligne de code testée
* **Régression protection** - Détection immédiate des régressions
* **Documentation vivante** - Tests expliquent le comportement

Validation :
* [ ] 3 classes développées entièrement en TDD
* [ ] Cycle Red-Green-Refactor appliqué 10+ fois
* [ ] Tests écrits avant le code de production
* [ ] Refactoring effectué sans casser les tests

=== T016: JUnit 5 Avancé (1h30)
[source,java]
----
@BeforeEach
void setUp() {
    game = new NumberGuesser(50);
}

@Test
@DisplayName("Should return TOO_HIGH when guess is above target")
void shouldReturnTooHighWhenGuessIsAboveTarget() {
    // Test implementation
}

@ParameterizedTest
@ValueSource(ints = {51, 75, 100})
void shouldReturnTooHighForHighGuesses(int guess) {
    assertEquals(GuessResult.TOO_HIGH, game.guess(guess));
}

@Test
void shouldThrowExceptionForNegativeGuess() {
    assertThrows(IllegalArgumentException.class,
                () -> game.guess(-1));
}

@Nested
@DisplayName("When target is 50")
class WhenTargetIs50 {
    @BeforeEach
    void setUp() {
        game = new NumberGuesser(50);
    }

    @Test
    void shouldReturnTooHigh() {
        assertEquals(GuessResult.TOO_HIGH, game.guess(75));
    }
}
----

Validation :
* [ ] 5+ types d'assertions utilisées
* [ ] Tests paramétrés implémentés
* [ ] Tests d'exceptions fonctionnels
* [ ] Organisation avec @Nested classes

=== T017: Projet Jeu de Devinette TDD (2h)
Architecture TDD :
----
src/main/java/com/guessinggame/
├── Main.java              // Point d'entrée
├── NumberGuesser.java     // Logique du jeu
├── GameSession.java       // Session de jeu
├── InputValidator.java    // Validation entrées
├── GameStatistics.java    // Statistiques
└── ConsoleUI.java         // Interface utilisateur

src/test/java/com/guessinggame/
├── NumberGuesserTest.java
├── GameSessionTest.java
├── InputValidatorTest.java
├── GameStatisticsTest.java
└── IntegrationTest.java
----

Spécifications du jeu :
1. **Génération nombre aléatoire** entre 1 et 100
2. **Interface console** pour saisir les tentatives
3. **Feedback** : "Trop haut", "Trop bas", "Correct"
4. **Compteur tentatives** et limite maximale
5. **Validation entrées** : nombres valides uniquement
6. **Statistiques** : meilleur score, moyenne

Développement TDD étape par étape :

**Étape 1 - NumberGuesser (30 min)**
[source,java]
----
@Test void shouldReturnTooHighWhenGuessIsAboveTarget()
@Test void shouldReturnTooLowWhenGuessisBelowTarget()
@Test void shouldReturnCorrectWhenGuessEqualsTarget()
@Test void shouldThrowExceptionForInvalidRange()
----

**Étape 2 - GameSession (30 min)**
[source,java]
----
@Test void shouldTrackNumberOfAttempts()
@Test void shouldEndGameWhenCorrectGuess()
@Test void shouldEndGameWhenMaxAttemptsReached()
@Test void shouldProvideGameHistory()
----

Validation :
* [ ] Application complète fonctionnelle
* [ ] Développée 100% en TDD
* [ ] Couverture de code > 90%
* [ ] Interface utilisateur intuitive

== 📚 Ressources d'Apprentissage

=== 🎯 Ressources Principales (À étudier dans l'ordre)

==== **1. Concepts TDD Fondamentaux (3h d'étude)**
* **Baeldung TDD Guide** ⭐⭐⭐⭐⭐
  - Lien : https://www.baeldung.com/java-test-driven-development
  - TDD en Java avec exemples concrets
  - Progression step-by-step
* **OpenClassrooms - Testez votre code Java** ⭐⭐⭐⭐⭐
  - Lien : https://openclassrooms.com/fr/courses/6100311-testez-votre-code-java-pour-realiser-des-applications-de-qualite
  - Cours complet en français avec exercices

==== **2. JUnit 5 Maîtrise (2h d'apprentissage)**
* **JUnit 5 User Guide** ⭐⭐⭐⭐⭐
  - Lien : https://junit.org/junit5/docs/current/user-guide/
  - Documentation officielle complète
  - Annotations et assertions détaillées

==== **3. Debugging VS Code (1h30 d'apprentissage)**
* **VS Code Java Debugging Guide** ⭐⭐⭐⭐⭐
  - Lien : https://code.visualstudio.com/docs/java/java-debugging
  - Guide officiel VS Code
  - Configuration et utilisation debugger

=== 📖 Ressources Approfondissement

==== **Pratique TDD Intensive**
* **Coding Dojo TDD Katas** ⭐⭐⭐⭐⭐
  - Lien : http://codingdojo.org/kata/
  - Exercices TDD progressifs (FizzBuzz, etc.)
* **TDD by Example - Kent Beck** ⭐⭐⭐⭐⭐
  - Livre de référence (bibliothèque)

=== 🔧 Outils et Support

==== **Extensions VS Code**
* **Java Test Runner** - Exécution tests
* **Java Debug** - Debugging avancé

==== **Aide Communautaire**
* **Stack Overflow** ⭐⭐⭐⭐⭐
  - Tags : [java] [junit] [tdd] [debugging]

== ⏱️ Estimation Temporelle
[cols="3,2,1", options="header"]
|===
| Tâche | Durée | Difficulté
| Debugging VS Code | 1h30 | 🟡 Moyen
| Introduction TDD | 2h | 🟡 Moyen
| JUnit 5 avancé | 1h30 | 🟡 Moyen
| Projet complet | 2h | 🔴 Difficile
| **Total** | **7h** | **🟡 Moyen**
|===

== 📊 Métriques de Succès
* Couverture de code > 90% sur le projet
* Tests passants 100% en continu
* Temps de build < 30 secondes
* Bugs détectés par tests avant production

== 🔗 Liens Connexes
* US002 - Syntaxe Java (prérequis)
* US005 - Tests Unitaires (suivante)
* US006 - Classes et Objets (suivante)
* Projet : 02-guessing-game-tdd
