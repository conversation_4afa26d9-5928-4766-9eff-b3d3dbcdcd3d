# 📋 Plan de Certification Java OCP + Portfolio Pro (12 mois – 6h/sem)

| Catégorie          | Détails                                                                                   |
| ------------------ | ----------------------------------------------------------------------------------------- |
| 🎯 Objectif Final  | Certification OCP Java SE 17 Developer (1Z0-829) + Portfolio GitHub attractif             |
| ⏳ Durée Estimée    | 12 mois (~50 semaines)                                                                    |
| ⌛ Rythme           | 6h/semaine (4h cours/étude + 2h portfolio)                                                |
| 💰 Coût            | Cours = 0 €, Examen = ~245 €                                                              |
| 📚 Ressources Clés | Oracle MyLearn, FreeCodeCamp, MOOC Helsinki, Baeldung, Java Brains, Doc Oracle, CodingBat |

---

## 🏗️ Phase 1 : Bases solides + debugging (Mois 1 à 4)

**Objectif** : maîtriser la syntaxe, Git, debugging, POO et les collections de base. **À la fin** → niveau OCFA + 5 projets portfolio + compétences debugging/Git.

|Semaines|Objectif Principal|Ressources Gratuites Clés|Validation des Acquis|Projets Portfolio|
|---|---|---|---|---|
|S1-S2|Environnement + Git/GitHub|FreeCodeCamp (Java Full Course, 2h)<br>Oracle MyLearn Java Foundations (Mod 1-2)<br>**Git Tutorial (Atlassian/GitHub Docs)**|Installer JDK, IDE, Git<br>Premier repo GitHub<br>5 petits programmes|**Projet 1** : Calculatrice Console + Git workflow|
|S3-S4|Contrôle + Debugging + Tests|Oracle MyLearn (Mod 3-4)<br>MOOC Helsinki (chap. 3-4)<br>**JUnit 5 Tutorial (Baeldung)**<br>**IDE Debugging Guide**|Maîtriser breakpoints, watch<br>Écrire premiers tests JUnit|**Projet 2** : Jeu de Devinette (TDD approach)|
|S5-S6|POO Fondamentales|FreeCodeCamp POO section<br>Oracle MyLearn (Mod 5-6)<br>MOOC Helsinki (chap. 5-6)|Classes, objets, encapsulation<br>Tests unitaires systématiques|**Projet 3** : Système Compte Bancaire (avec tests)|
|S7-S8|Héritage & Polymorphisme|Doc Oracle : Héritage<br>Baeldung : OOP Basics<br>Java Brains (OOP vidéos)|Hiérarchie animaux avec polymorphisme<br>Tests d'héritage|**Projet 4** : Zoo Virtuel (hiérarchie complète)|
|S9-S11|Collections Fondamentales|FreeCodeCamp (ArrayList, HashMap)<br>Oracle MyLearn (Collections)<br>MOOC Helsinki (chap. 7-8)<br>CodingBat (Collections)|Maîtriser List, Set, Map<br>Algorithmes de tri basiques|**Projet 5** : Carnet d'Adresses (avec recherche)|
|S12-S14|Exceptions + I/O Basique|Baeldung (Exception Handling)<br>Oracle MyLearn (Exceptions)<br>Java I/O Tutorial|Gestion robuste des erreurs<br>Lecture/écriture fichiers|**Projet PHARE 1** : Gestionnaire de Tâches (fichier + exceptions)|
|S15-S16|Projet Synthèse Phase 1|Toutes ressources précédentes|App console complète avec persistance|**Projet PHARE 2** : Système Bibliothèque Console|

---

## ⚡ Phase 2 : Java SE 17 Avancé (Mois 5 à 8)

**Objectif** : maîtriser les features Java SE 17 pour l'OCP + projets plus complexes. **Focus OCP** : pas de frameworks externes, Java SE pur.

|Semaines|Objectif Principal|Ressources Gratuites Clés|Validation des Acquis|Projets Portfolio|
|---|---|---|---|---|
|S17-S19|Generics + Collections Avancées|Baeldung (Collections, Generics)<br>Java Brains (Generics vidéos)<br>Oracle MyLearn (Generics)<br>CodingBat (exercices avancés)|Créer classes génériques<br>Bounded wildcards|**Projet 6** : Cache LRU générique<br>**Projet 7** : Analyseur CSV générique|
|S20-S22|Streams & Lambdas (Java 8+)|Oracle MyLearn Java SE Programming<br>Baeldung (Streams Guide)<br>Java Brains (Streams/Lambda vidéos)|Refactoring avec Streams<br>Method references|**Projet 8** : Analyseur de Logs (Streams intensif)|
|S23-S25|I/O & NIO.2|Doc Oracle NIO.2<br>Baeldung (java.nio.file)<br>Java Brains (NIO vidéos)|File operations avancées<br>Path API mastery|**Projet 9** : File Backup Manager|
|S26-S28|Concurrence (Threads, Executor)|Baeldung (Concurrency)<br>Oracle MyLearn (Concurrency)<br>Java Brains (ExecutorService)|Thread safety<br>Parallel processing|**Projet 10** : Serveur Chat Multi-clients (NIO)|
|S29-S31|Modules Java (Jigsaw)|Oracle MyLearn (Project Jigsaw)<br>Baeldung (Java Modules)<br>Doc Oracle Modules|Créer applications modulaires<br>Module path vs classpath|**Projet 11** : Application Modulaire (2-3 modules)|
|S32|**Features Java SE 17**|Oracle Java 17 Documentation<br>Baeldung (Java 17 features)<br>**Sealed Classes, Pattern Matching**|Records, sealed classes<br>Switch expressions|**Projet PHARE 3** : Mini-Framework avec Records/Sealed|

---

## 🎯 Phase 3 : Préparation OCP + Spring Boot (Mois 9 à 12)

**Objectif** : réussir l'OCP + créer portfolio moderne avec Spring Boot. **Split Track** : Core Java (OCP focus) + Modern Portfolio.

|Semaines|Préparation Examen OCP (4h)|Portfolio Moderne (2h)|Projets|
|---|---|---|---|
|S33-S35|**Révisions Core Java SE 17**<br>Enthuware Mock Tests (chapitre par chapitre)<br>Oracle Study Guide review|**Introduction Spring Boot**<br>Spring.io Getting Started<br>Java Brains Spring Boot|**Projet PHARE 4** : REST API Simple (Spring Boot)|
|S36-S38|**Examens blancs complets**<br>Whizlabs Practice Tests<br>Analyse erreurs + révisions ciblées|**Spring Boot + Base de données**<br>Spring Data JPA<br>H2/PostgreSQL|**Projet PHARE 5** : API Gestion Employés (CRUD complet)|
|S39-S41|**Révisions intensives OCP**<br>Points faibles identifiés<br>Flashcards concepts clés|**Sécurité + Tests Spring**<br>Spring Security basics<br>@SpringBootTest|**Projet PHARE 6** : E-Commerce Backend (sécurisé)|
|S42-S44|**Passage Examen OCP** 📜<br>Dernières révisions<br>**EXAMEN OFFICIEL**|**Portfolio Web Personnel**<br>Frontend simple (Thymeleaf ou HTML/CSS)<br>Déploiement (Heroku/Railway)|**Projet PHARE 7** : Site Portfolio Personnel|
|S45-S48|**Post-OCP : Optimisation Portfolio**<br>Documentation projets<br>LinkedIn/CV optimization|**Projets avancés**<br>Microservices intro<br>Docker basics|**Projet FINAL** : Mini-Architecture Microservices|
|S49-S50|**Recherche d'emploi**<br>Applications actives<br>Préparation entretiens techniques|**GitHub polish**<br>Vidéos démo<br>README professionnels|**Portfolio Final** : 10-12 projets documentés|

---

## 🎨 Structure du Portfolio GitHub Final

### Organisation

- **README principal** : Présentation, stack technique, projets phares
- **Dossier par projet** avec structure standard :

    ```
    projet-nom/├── README.md (description, installation, captures)├── src/ (code source organisé)├── tests/ (JUnit 5 comprehensive)├── docs/ (documentation technique)└── screenshots/ (démos visuelles)
    ```


### Projets Phares (10-12 total)

1. **Console Apps** (3) : Calculatrice, Jeu, Bibliothèque
2. **Core Java** (4) : Cache LRU, Analyseur CSV/Logs, Backup Manager
3. **Concurrence** (2) : Chat Server, Application Modulaire
4. **Spring Boot** (3-4) : REST API, E-Commerce, Portfolio Web, Microservices

---

## ✅ Résultat Final (12 mois)

### Certification & Compétences

- ✅ **Certification OCP Java SE 17 Developer** obtenue
- ✅ **Maîtrise complète** : Java SE 17, debugging, Git, tests unitaires
- ✅ **Compétences modernes** : Spring Boot, REST APIs, bases de données

### Portfolio Professionnel

- ✅ **GitHub attractif** : 10-12 projets documentés et testés
- ✅ **Progression visible** : de console apps à microservices
- ✅ **CV tech optimisé** : prêt pour le marché Java développeur

### Avantages des Modifications

- 🔧 **Debugging dès le début** → moins de frustration
- 🧪 **Tests précoces** → bonnes pratiques ancrées
- 📚 **Focus OCP renforcé** → meilleur taux de réussite examen
- 🚀 **Spring Boot en fin** → fondations solides d'abord
- ⏱️ **Pacing réaliste** → moins de rush, meilleure assimilation

---