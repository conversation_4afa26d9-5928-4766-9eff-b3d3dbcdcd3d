Arborescence Complète du Repository

```
java-ocp-certification-journey/
├── README.md                          # 🏠 ACCUEIL PORTFOLIO
├── docs/
│   ├── LEARNING-PLAN.md              # Plan d'apprentissage détaillé
│   ├── PROGRESS-TRACKER.md           # Journal de progression
│   ├── CERTIFICATION-GUIDE.md        # Guide examen OCP
│   └── RESOURCES.md                  # Ressources d'apprentissage
├── .github/
│   └── templates/
│       ├── project-readme-template.md
│       └── weekly-report-template.md
├── templates/
│   ├── maven-java-project/
│   ├── spring-boot-starter/
│   └── junit5-test-setup/
│
├── phase-1-fundamentals/             # 📚 PHASE 1 (16 semaines)
│   ├── README.md                     # Vue d'ensemble Phase 1
│   ├── week-01-02-environment/
│   │   ├── README.md                 # Objectifs semaines 1-2
│   │   ├── setup-guide/
│   │   ├── git-workflow/
│   │   └── projects/
│   │       └── 01-calculator-console/
│   │           ├── README.md         # Description projet
│   │           ├── src/
│   │           ├── test/
│   │           └── docs/
│   ├── week-03-04-debugging/
│   │   ├── README.md
│   │   ├── debugging-techniques/
│   │   └── projects/
│   │       └── 02-guessing-game-tdd/
│   │           ├── README.md
│   │           ├── src/
│   │           ├── test/
│   │           └── docs/
│   ├── week-05-06-oop-basics/
│   │   └── [même structure...]
│   ├── week-07-08-inheritance/
│   ├── week-09-11-collections/
│   ├── week-12-14-exceptions-io/
│   └── week-15-16-synthesis/
│
├── phase-2-advanced-java/            # 🚀 PHASE 2 (16 semaines)
│   ├── README.md
│   ├── week-17-19-generics/
│   ├── week-20-22-streams-lambdas/
│   ├── week-23-25-io-nio2/
│   ├── week-26-28-concurrency/
│   ├── week-29-31-modules/
│   └── week-32-synthesis/
│
├── phase-3-ocp-preparation/          # 📋 PHASE 3 (12 semaines)
│   ├── README.md
│   ├── week-33-35-review/
│   ├── week-36-38-mock-exams/
│   ├── week-39-41-intensive/
│   ├── week-42-44-final-prep/
│   └── certification-materials/
│
├── phase-4-spring-portfolio/         # 🌱 PHASE 4 (16 semaines)
│   ├── README.md
│   ├── week-33-35-spring-intro/      # Parallèle à Phase 3
│   ├── week-36-38-spring-data/
│   ├── week-39-41-security-testing/
│   ├── week-42-44-web-deployment/
│   ├── week-45-48-microservices/
│   └── week-49-50-optimization/
│
└── milestone-projects/               # 🏆 PROJETS PHARES
    ├── README.md                     # Index des projets
    ├── console-applications/         # Niveau 1: Applications console
    │   ├── README.md
    │   ├── 01-calculator/
    │   ├── 02-guessing-game/
    │   ├── 03-banking-system/
    │   ├── 04-virtual-zoo/
    │   ├── 05-address-book/
    │   ├── 06-task-manager/
    │   └── 07-library-system/
    ├── core-java-projects/           # Niveau 2: Java SE avancé
    │   ├── README.md
    │   ├── 08-lru-cache/
    │   ├── 09-csv-analyzer/
    │   ├── 10-log-analyzer/
    │   ├── 11-backup-manager/
    │   ├── 12-chat-server/
    │   ├── 13-modular-application/
    │   └── 14-mini-framework/
    └── spring-boot-applications/     # Niveau 3: Applications modernes
        ├── README.md
        ├── 15-rest-api-simple/
        ├── 16-employee-management/
        ├── 17-ecommerce-backend/
        ├── 18-portfolio-website/
        └── 19-microservices-demo/
```

---

## 📋 Templates de README par Niveau

### 🏠 README.md Principal (Racine)

```markdown
# 🏆 Java OCP Certification Journey - Portfolio Professionnel

[![OCP Java SE 17](https://img.shields.io/badge/OCP-Java%20SE%2017-orange?style=for-the-badge&logo=oracle)](https://education.oracle.com/oracle-certified-professional-java-se-17-developer/trackp_OCPJSE17)
[![Progress](https://img.shields.io/badge/Progress-45%25-green?style=for-the-badge)](./docs/PROGRESS-TRACKER.md)
[![Projects](https://img.shields.io/badge/Projects-12/19-blue?style=for-the-badge)](#projets-réalisés)

> **Mission :** Obtenir la certification Oracle Certified Professional Java SE 17 Developer tout en construisant un portfolio GitHub professionnel et attractif.

## 👨‍💻 À Propos

Développeur en formation Java SE 17, je documente ici mon parcours de 12 mois vers la certification OCP et la maîtrise des technologies Java modernes. Ce repository contient tous mes projets d'apprentissage, du niveau console aux microservices Spring Boot.

**🎯 Objectifs :**
- [x] Maîtriser Java SE 17 (syntaxe → concepts avancés)
- [ ] Obtenir la certification OCP Java SE 17 Developer (1Z0-829)
- [ ] Construire 19 projets progressifs et documentés
- [ ] Développer une expertise Spring Boot employable

## 🚀 Progression Actuelle

### Phase 1: Fondamentaux ✅ **Terminée**
**Durée :** 16 semaines (Jan - Avr 2024)
**Focus :** Syntaxe Java, POO, Collections, Exceptions, I/O
**Projets :** 7/7 applications console complètes

### Phase 2: Java SE 17 Avancé 🔄 **En cours**
**Durée :** 16 semaines (Mai - Août 2024)
**Focus :** Generics, Streams, Concurrence, Modules
**Projets :** 5/7 applications Java SE avancées

### Phase 3: Préparation OCP ⏳ **À venir**
**Durée :** 12 semaines (Sep - Nov 2024)
**Focus :** Révisions, examens blancs, certification
**Objectif :** Score 80%+ et certification obtenue

### Phase 4: Portfolio Spring Boot ⏳ **À venir**
**Durée :** 16 semaines (Sep - Déc 2024)
**Focus :** Spring Boot, REST APIs, Microservices
**Projets :** 0/5 applications web modernes

## 📊 Projets Réalisés

| Niveau | Projet | Statut | Technologies | Description |
|--------|---------|---------|--------------|-------------|
| **Console** | [Calculatrice](./milestone-projects/console-applications/01-calculator/) | ✅ | Java, JUnit, Git | Premier projet avec TDD |
| **Console** | [Jeu Devinette](./milestone-projects/console-applications/02-guessing-game/) | ✅ | Java, JUnit, Debugging | Approche TDD + debugging |
| **Console** | [Système Bancaire](./milestone-projects/console-applications/03-banking-system/) | ✅ | Java, POO, Tests | Encapsulation et validation |
| **Console** | [Zoo Virtuel](./milestone-projects/console-applications/04-virtual-zoo/) | ✅ | Java, Héritage, Polymorphisme | Hiérarchie d'objets complexe |
| **Console** | [Carnet d'Adresses](./milestone-projects/console-applications/05-address-book/) | ✅ | Java, Collections, Recherche | ArrayList et HashMap |
| **Console** | [Gestionnaire de Tâches](./milestone-projects/console-applications/06-task-manager/) | ✅ | Java, I/O, Exceptions | Persistance fichier + gestion erreurs |
| **Console** | [Système Bibliothèque](./milestone-projects/console-applications/07-library-system/) | ✅ | Java, Architecture, Tests | Projet de synthèse Phase 1 |
| **Core Java** | [Cache LRU](./milestone-projects/core-java-projects/08-lru-cache/) | ✅ | Generics, Collections | Implémentation cache générique |
| **Core Java** | [Analyseur CSV](./milestone-projects/core-java-projects/09-csv-analyzer/) | ✅ | Generics, Streams | Processing données avec Streams |
| **Core Java** | [Analyseur Logs](./milestone-projects/core-java-projects/10-log-analyzer/) | 🔄 | Streams, Lambdas | Analyse logs avec API Streams |
| **Core Java** | Backup Manager | ⏳ | I/O, NIO.2 | - |
| **Core Java** | Chat Server | ⏳ | Concurrence, NIO | - |
| **Spring** | REST API Simple | ⏳ | Spring Boot, Web | - |

## 🛠️ Stack Technique

### Langages & Versions
- **Java SE 17** (LTS) - Niveau certification
- **Maven 3.9+** - Gestion des dépendances
- **Git 2.40+** - Contrôle de version

### Frameworks & Librairies
- **JUnit 5** + **Mockito** - Tests unitaires et mocks
- **Spring Boot 3.x** - Framework web moderne
- **Spring Data JPA** - Persistance base de données
- **H2 / PostgreSQL** - Bases de données

### Outils de Développement
- **IntelliJ IDEA** - IDE principal
- **VS Code** - Éditeur léger
- **GitHub Actions** - CI/CD (à venir)
- **Docker** - Containerisation (à venir)

## 📚 Documentation

- [📖 Plan d'Apprentissage Détaillé](./docs/LEARNING-PLAN.md)
- [📊 Tracker de Progression](./docs/PROGRESS-TRACKER.md)
- [🎓 Guide Certification OCP](./docs/CERTIFICATION-GUIDE.md)
- [📚 Ressources d'Apprentissage](./docs/RESOURCES.md)

## 🔗 Navigation Rapide

### Par Phase d'Apprentissage
- [Phase 1: Fondamentaux](./phase-1-fundamentals/) - Syntaxe, POO, Collections
- [Phase 2: Java Avancé](./phase-2-advanced-java/) - Streams, Concurrence, Modules
- [Phase 3: Préparation OCP](./phase-3-ocp-preparation/) - Révisions, Examens blancs
- [Phase 4: Spring Portfolio](./phase-4-spring-portfolio/) - Spring Boot, REST, Microservices

### Par Type de Projet
- [Applications Console](./milestone-projects/console-applications/) - 7 projets fondamentaux
- [Projets Core Java](./milestone-projects/core-java-projects/) - 7 projets avancés
- [Applications Spring Boot](./milestone-projects/spring-boot-applications/) - 5 projets modernes

## 📞 Contact & Réseaux

- **LinkedIn :** [Mon Profil](https://linkedin.com/in/monprofil)
- **Email :** <EMAIL>
- **Portfolio Web :** [monportfolio.dev](https://monportfolio.dev) *(à venir)*

---

⭐ **N'hésitez pas à explorer mes projets et à me faire des retours !**

*Dernière mise à jour : [Date] - Projet en cours : [Nom du projet]*
```

### 📚 README.md des Phases (Exemple Phase 1)

```markdown
# 📚 Phase 1 : Fondamentaux Java (Semaines 1-16)

[![Phase Status](https://img.shields.io/badge/Status-Completed-success?style=for-the-badge)](../docs/PROGRESS-TRACKER.md)
[![Projects](https://img.shields.io/badge/Projects-7/7-green?style=for-the-badge)](#projets-réalisés)
[![Duration](https://img.shields.io/badge/Duration-16%20weeks-blue?style=for-the-badge)](./README.md)

## 🎯 Objectifs de la Phase

À la fin de cette phase de 4 mois, je maîtrise :
- ✅ **Syntaxe Java complète** - Variables, opérateurs, conditions, boucles
- ✅ **Programmation Orientée Objet** - Classes, objets, héritage, polymorphisme
- ✅ **Collections fondamentales** - ArrayList, HashMap, algorithmes de base
- ✅ **Gestion des erreurs** - Try-catch, exceptions personnalisées
- ✅ **Entrées/Sorties** - Lecture/écriture fichiers, persistance
- ✅ **Debugging avancé** - Breakpoints, inspection variables, stack traces
- ✅ **Tests unitaires** - JUnit 5, assertions, couverture de code
- ✅ **Git workflow** - Commits, branches, collaboration GitHub

## 📅 Planning Détaillé

| Période | Module | Focus Principal | Projet Livré | Statut |
|---------|--------|-----------------|---------------|---------|
| **S1-S2** | [Environment & Git](./week-01-02-environment/) | Installation, Git, premier code | [Calculatrice Console](./week-01-02-environment/projects/01-calculator-console/) | ✅ |
| **S3-S4** | [Debugging & TDD](./week-03-04-debugging/) | Debugging, tests JUnit | [Jeu Devinette TDD](./week-03-04-debugging/projects/02-guessing-game-tdd/) | ✅ |
| **S5-S6** | [POO Basics](./week-05-06-oop-basics/) | Classes, objets, encapsulation | [Système Bancaire](./week-05-06-oop-basics/projects/03-banking-system/) | ✅ |
| **S7-S8** | [Inheritance](./week-07-08-inheritance/) | Héritage, polymorphisme | [Zoo Virtuel](./week-07-08-inheritance/projects/04-virtual-zoo/) | ✅ |
| **S9-S11** | [Collections](./week-09-11-collections/) | ArrayList, HashMap, recherche | [Carnet d'Adresses](./week-09-11-collections/projects/05-address-book/) | ✅ |
| **S12-S14** | [Exceptions & I/O](./week-12-14-exceptions-io/) | Try-catch, fichiers | [Gestionnaire Tâches](./week-12-14-exceptions-io/projects/06-task-manager/) | ✅ |
| **S15-S16** | [Synthèse](./week-15-16-synthesis/) | Projet complet | [Système Bibliothèque](./week-15-16-synthesis/projects/07-library-system/) | ✅ |

## 🏆 Projets Réalisés

### Applications Console (7/7)
| # | Projet | Concepts Clés | Lignes de Code | Tests | Démo |
|---|---------|---------------|----------------|--------|------|
| 1 | [Calculatrice](./week-01-02-environment/projects/01-calculator-console/) | Syntaxe, Git workflow | ~150 | 15 tests | [▶️ Demo](lien) |
| 2 | [Jeu Devinette](./week-03-04-debugging/projects/02-guessing-game-tdd/) | TDD, debugging | ~200 | 20 tests | [▶️ Demo](lien) |
| 3 | [Système Bancaire](./week-05-06-oop-basics/projects/03-banking-system/) | POO, encapsulation | ~300 | 25 tests | [▶️ Demo](lien) |
| 4 | [Zoo Virtuel](./week-07-08-inheritance/projects/04-virtual-zoo/) | Héritage, polymorphisme | ~400 | 30 tests | [▶️ Demo](lien) |
| 5 | [Carnet Adresses](./week-09-11-collections/projects/05-address-book/) | Collections, recherche | ~350 | 28 tests | [▶️ Demo](lien) |
| 6 | [Gestionnaire Tâches](./week-12-14-exceptions-io/projects/06-task-manager/) | I/O, exceptions | ~500 | 40 tests | [▶️ Demo](lien) |
| 7 | [Système Bibliothèque](./week-15-16-synthesis/projects/07-library-system/) | Architecture complète | ~800 | 60 tests | [▶️ Demo](lien) |

## 📊 Statistiques de la Phase

### Métriques de Code
- **Total lignes de code :** ~2,700 lignes Java
- **Tests écrits :** 218 tests unitaires
- **Couverture moyenne :** 85%+ sur tous les projets
- **Commits Git :** 127 commits bien documentés

### Compétences Acquises
- **Syntaxe Java :** ⭐⭐⭐⭐⭐ (Maîtrise complète)
- **POO :** ⭐⭐⭐⭐⭐ (4 piliers maîtrisés)
- **Collections :** ⭐⭐⭐⭐⭐ (ArrayList, HashMap, Set)
- **Debugging :** ⭐⭐⭐⭐⭐ (Outils IDE + techniques)
- **Tests JUnit :** ⭐⭐⭐⭐⭐ (TDD + couverture)
- **Git/GitHub :** ⭐⭐⭐⭐⭐ (Workflow complet)

### Temps d'Apprentissage
- **Heures totales :** 96h (6h × 16 semaines)
- **Répartition :** 64h étude + 32h projets
- **Rythme moyen :** 6h/semaine maintenu

## 🔗 Navigation

### Modules de la Phase 1
- [Semaines 1-2: Environment & Git](./week-01-02-environment/) - Setup + Premier projet
- [Semaines 3-4: Debugging & TDD](./week-03-04-debugging/) - Debug + Tests
- [Semaines 5-6: POO Basics](./week-05-06-oop-basics/) - Classes et objets
- [Semaines 7-8: Inheritance](./week-07-08-inheritance/) - Héritage et polymorphisme
- [Semaines 9-11: Collections](./week-09-11-collections/) - ArrayList et HashMap
- [Semaines 12-14: Exceptions & I/O](./week-12-14-exceptions-io/) - Erreurs et fichiers
- [Semaines 15-16: Synthèse](./week-15-16-synthesis/) - Projet complet

### Autres Phases
- [🚀 Phase 2: Java Avancé](../phase-2-advanced-java/) - Generics, Streams, Concurrence
- [📋 Phase 3: Préparation OCP](../phase-3-ocp-preparation/) - Examen et certification
- [🌱 Phase 4: Spring Portfolio](../phase-4-spring-portfolio/) - Applications web modernes

## 📚 Ressources Utilisées

### Cours Principaux
- **Oracle MyLearn** - Java Foundations (modules 1-8)
- **FreeCodeCamp** - Java Full Course (14h de vidéo)
- **MOOC Helsinki** - Java Programming I (chapitres 1-8)

### Pratique & Exercices
- **CodingBat** - Java String-1, Logic-1, Array-1
- **HackerRank** - Java Domain (10 Days of Code)
- **LeetCode** - Easy problems (Arrays, Strings)

### Documentation
- **Oracle Java Documentation** - API officielle
- **Baeldung** - Tutorials POO, Collections, I/O
- **Java Brains** - Vidéos YouTube sur concepts clés

## ✅ Prérequis pour Phase 2

Avant de passer à la Phase 2 (Java Avancé), je maîtrise :

- [x] **Syntaxe Java complète** - Variables à méthodes
- [x] **POO fondamentale** - 4 piliers + interfaces
- [x] **Collections de base** - List, Set, Map usage
- [x] **Gestion exceptions** - Try-catch + exceptions métier
- [x] **I/O basique** - Lecture/écriture fichiers texte
- [x] **Tests JUnit 5** - Assertions + configuration
- [x] **Git workflow** - Branch, commit, merge, PR

**🎯 Prêt pour la Phase 2 !** → [Java SE 17 Avancé](../phase-2-advanced-java/)

---

*Phase 1 complétée le [Date] - Passage en Phase 2 le [Date]*
```

### 📅 README.md des Semaines (Exemple Semaine 1-2)

```markdown
# 📅 Semaines 1-2 : Environment Setup & Git Workflow

[![Week Status](https://img.shields.io/badge/Status-Completed-success?style=flat-square)](../../docs/PROGRESS-TRACKER.md)
[![Project](https://img.shields.io/badge/Project-Calculator%20Console-blue?style=flat-square)](./projects/01-calculator-console/)

## 🎯 Objectifs de la Période

**Focus principal :** Préparer l'environnement de développement parfait et maîtriser Git/GitHub pour un workflow professionnel dès le début.

### Compétences Visées
- [x] **Installation environnement** - JDK 17, IntelliJ IDEA, Git
- [x] **Configuration IDE** - Plugins, thème, raccourcis clavier
- [x] **Maîtrise Git** - Init, add, commit, push, pull, branch
- [x] **GitHub workflow** - Création repo, README, issues, releases
- [x] **Premier programme** - Hello World → Calculatrice avec tests
- [x] **Debugging IDE** - Breakpoints, step-by-step, variables watch

## 📚 Programme d'Apprentissage

### Semaine 1 : Installation & Configuration
#### Jour 1-2 : Environnement Java (4h)
- **Installation JDK 17** - Oracle JDK vs OpenJDK, PATH, JAVA_HOME
- **Setup IntelliJ IDEA** - Installation, première configuration
- **Ressources :**
  - [Oracle JDK Download](https://www.oracle.com/java/technologies/downloads/)
  - [IntelliJ IDEA Setup Guide](https://www.jetbrains.com/help/idea/getting-started.html)

#### Jour 3-4 : Git & GitHub (4h)
- **Installation Git** - Configuration user.name, user.email
- **Concepts de base** - Repository, staging, commits, remote
- **Ressources :**
  - [Git Documentation](https://git-scm.com/doc)
  - [GitHub Getting Started](https://docs.github.com/en/get-started)

#### Jour 5-7 : Premier Code Java (4h)
- **Hello World** - Structure d'un programme, public static void main
- **Syntaxe de base** - Variables, types primitifs, opérateurs
- **Ressources :**
  - [FreeCodeCamp Java Course](https://www.youtube.com/watch?v=grEKMHGYyns) (0-2h)
  - [Oracle MyLearn Module 1](https://mylearn.oracle.com/ou/course/java-se-foundations/121733/)

### Semaine 2 : Développement & Tests
#### Jour 8-10 : Logique & Conditions (4h)
- **Instructions conditionnelles** - if/else, switch, opérateurs logiques
- **Boucles** - for, while, do-while
- **Ressources :**
  - Oracle MyLearn Module 2
  - CodingBat Logic-1

#### Jour 11-12 : Tests & Debugging (4h)
- **Introduction JUnit 5** - @Test, assertions, configuration
- **Debugging IDE** - Breakpoints, step over/into, variables
- **Ressources :**
  - [JUnit 5 Guide](https://junit.org/junit5/docs/current/user-guide/)
  - [IntelliJ Debugging](https://www.jetbrains.com/help/idea/debugging-code.html)

#### Jour 13-14 : Projet Final (4h)
- **Calculatrice Console** - Design, implémentation, tests
- **Git workflow** - Commits réguliers, branches, release

## 🏆 Projet : Calculatrice Console

### Description
Application console qui effectue les 4 opérations de base (+, -, *, /) avec gestion des erreurs et tests unitaires complets.

### Fonctionnalités
- [x] **Interface utilisateur** - Menu console intuitif
- [x] **4 opérations** - Addition, soustraction, multiplication, division
- [x] **Gestion erreurs** - Division par zéro, saisies invalides
- [x] **Tests JUnit** - Couverture 90%+ de toutes les méthodes
- [x] **Git workflow** - Commits atomiques, branches features

### Structure du Code
```

01-calculator-console/ ├── README.md # Documentation complète ├── pom.xml # Configuration Maven ├── src/ │ ├── main/java/com/calculator/ │ │ ├── Calculator.java # Logique métier │ │ ├── ConsoleUI.java # Interface utilisateur │ │ └── Main.java # Point d'entrée │ └── test/java/com/calculator/ │ ├── CalculatorTest.java # Tests unitaires │ └── ConsoleUITest.java # Tests UI └── docs/ ├── user-guide.md # Guide utilisateur └── technical-specs.md # Spécifications techniques

````

### Démonstration

```bash
=== CALCULATRICE CONSOLE ===
1. Addition
2. Soustraction
3. Multiplication
4. Division
5. Quitter

Votre choix: 1
Premier nombre: 15.5
Deuxième nombre: 7.2
Résultat: 15.5 + 7.2 = 22.7

Continuer? (o/n): n
Au revoir!
````

### Métriques Finales

- **Lignes de code :** 150 lignes Java
- **Tests :** 15 tests unitaires (couverture 92%)
- **Commits Git :** 8 commits bien documentés
- **Temps développement :** 8 heures

## ✅ Validation des Acquis

### Auto-évaluation (1-5 étoiles)

- **Installation environnement :** ⭐⭐⭐⭐⭐
- **Syntaxe Java de base :** ⭐⭐⭐⭐⭐
- **Git commands :** ⭐⭐⭐⭐⭐
- **GitHub workflow :** ⭐⭐⭐⭐⭐
- **Tests JUnit :** ⭐⭐⭐⭐⭐
- **Debugging IDE :** ⭐⭐⭐⭐⭐

### Quiz de Fin de Module

- [x] **Question 1 :** Différence entre JDK, JRE et JVM ? ✅
- [x] **Question 2 :** Commandes Git essentielles ? ✅
- [x] **Question 3 :** Structure d'un test JUnit ? ✅
- [x] **Question 4 :** Comment débugger avec breakpoints ? ✅
- [x] **Question 5 :** Gestion division par zéro ? ✅

**Score final :** 5/5 ✅ **Prêt pour les Semaines 3-4 !**

## 📚 Ressources Utilisées

### Cours Vidéo

- [FreeCodeCamp Java Full Course](https://www.youtube.com/watch?v=grEKMHGYyns) (0-2h)
- [Derek Banas Java Tutorial](https://www.youtube.com/watch?v=TBWX97e1E9g) (syntaxe)

### Documentation Officielle

- [Oracle Java 17 Documentation](https://docs.oracle.com/en/java/javase/17/)
- [Git Documentation](https://git-scm.com/doc)
- [JUnit 5 User Guide](https://junit.org/junit5/docs/current/user-guide/)

### Exercices Pratiques

- [CodingBat Java](https://codingbat.com/java) - Warmup-1, Logic-1
- [W3Schools Java](https://www.w3schools.com/java/) - Syntaxe et exemples