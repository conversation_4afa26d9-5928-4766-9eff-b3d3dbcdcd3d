# 📖 Plan d'Apprentissage Détaillé - Java OCP Certification Journey

[![Learning Plan](https://img.shields.io/badge/Status-Active-success?style=for-the-badge)](../README.md)
[![Duration](https://img.shields.io/badge/Duration-12%20months-blue?style=for-the-badge)](./PROGRESS-TRACKER.md)

## 🎯 Vue d'Ensemble

Ce plan d'apprentissage de 12 mois est conçu pour maîtriser Java SE 17 et obtenir la certification Oracle Certified Professional (OCP) tout en construisant un portfolio GitHub professionnel.

### Objectifs Principaux
1. **Maîtrise technique** - Java SE 17 de la syntaxe aux concepts avancés
2. **Certification OCP** - Obtenir la certification 1Z0-829 avec score 80%+
3. **Portfolio professionnel** - 19 projets progressifs et documentés
4. **Employabilité** - Compétences Spring Boot et architecture moderne

### Méthodologie d'Apprentissage
- **Apprentissage par projet** - Chaque concept appris via un projet concret
- **Test-Driven Development** - TDD dès le premier projet
- **Documentation continue** - README détaillé pour chaque projet
- **Git workflow professionnel** - Commits atomiques, branches, releases

## 📅 Planning Global (52 semaines)

### Phase 1: Fondamentaux Java (16 semaines)
**Période :** Semaines 1-16
**Objectif :** Maîtriser les bases solides de Java SE 17

| Semaines | Module | Focus Principal | Projet Livré |
|----------|--------|-----------------|---------------|
| 1-2 | Environment & Git | Setup, Git workflow | Calculatrice Console |
| 3-4 | Debugging & TDD | Tests JUnit, debugging | Jeu Devinette TDD |
| 5-6 | POO Basics | Classes, objets, encapsulation | Système Bancaire |
| 7-8 | Inheritance | Héritage, polymorphisme | Zoo Virtuel |
| 9-11 | Collections | ArrayList, HashMap, algorithmes | Carnet d'Adresses |
| 12-14 | Exceptions & I/O | Try-catch, fichiers | Gestionnaire Tâches |
| 15-16 | Synthèse | Architecture complète | Système Bibliothèque |

**📚 Ressources Gratuites Phase 1 :**
- **🎯 Principal :** [Oracle Java Foundations](https://education.oracle.com/java-foundations/pexam_1Z0-811) (20h + certification gratuite)
- **📺 Vidéo :** [FreeCodeCamp Java Course](https://www.youtube.com/watch?v=grEKMHGYyns) (14h complètes)
- **💻 Pratique :** [MOOC Helsinki](https://java-programming.mooc.fi/) (exercices automatisés)
- **🧩 Exercices :** [CodingBat Java](https://codingbat.com/java) (pratique quotidienne)
- **📖 Référence :** [Oracle Java Documentation](https://docs.oracle.com/en/java/javase/17/)
- **🎓 Certification :** Oracle Java Foundations (1Z0-811) - Gratuite !

### Phase 2: Java SE 17 Avancé (16 semaines)
**Période :** Semaines 17-32
**Objectif :** Concepts avancés et préparation certification

| Semaines | Module | Focus Principal | Projet Livré |
|----------|--------|-----------------|---------------|
| 17-19 | Generics | Types génériques, wildcards | Cache LRU |
| 20-22 | Streams & Lambdas | API Streams, expressions lambda | Analyseur CSV |
| 23-25 | I/O & NIO.2 | Files, Paths, streams | Analyseur Logs |
| 26-28 | Concurrency | Threads, ExecutorService | Backup Manager |
| 29-31 | Modules | JPMS, modularité | Chat Server |
| 32 | Synthèse | Révision concepts | Application Modulaire |

**📚 Ressources Gratuites Phase 2 :**
- **🎯 Principal :** [Oracle MyLearn Java SE 17](https://mylearn.oracle.com/) (40h + labs)
- **📺 Vidéo :** [Java Brains YouTube](https://www.youtube.com/c/JavaBrainsChannel) (concepts avancés)
- **💻 Pratique :** [HackerRank Java](https://www.hackerrank.com/domains/java) (algorithmes)
- **📖 Tutoriels :** [Baeldung](https://www.baeldung.com/) (articles détaillés)
- **🧩 Exercices :** [LeetCode](https://leetcode.com/) (structures de données)
- **📚 Livre :** [Effective Java PDF](https://github.com/clxering/Effective-Java-3rd-edition-Chinese-English-bilingual) (gratuit)

### Phase 3: Préparation OCP (12 semaines)
**Période :** Semaines 33-44
**Objectif :** Certification Oracle OCP Java SE 17

| Semaines | Module | Focus Principal | Activité |
|----------|--------|-----------------|----------|
| 33-35 | Review Fundamentals | Révision Phase 1 | Examens blancs |
| 36-38 | Review Advanced | Révision Phase 2 | Mock exams |
| 39-41 | Intensive Practice | Points faibles | Exercices ciblés |
| 42-44 | Final Preparation | Simulation examen | Certification |

**📚 Ressources Gratuites Phase 3 :**
- **🎯 Principal :** [Oracle Sample Questions](https://education.oracle.com/java-se-17-developer/pexam_1Z0-829) (questions officielles)
- **📺 Révision :** [Oracle MyLearn](https://mylearn.oracle.com/) (révision complète)
- **💻 Mock Exams :** [JavaRanch Mock Exams](https://coderanch.com/wiki/718759/Mock-Exams) (gratuits)
- **📖 Study Guide :** [Oracle Certification Guide](https://docs.oracle.com/en/java/javase/17/) (documentation)
- **🧩 Practice :** [CodeGym](https://codegym.cc/) (exercices ciblés)
- **📚 Forums :** [CodeRanch](https://coderanch.com/) (communauté certification)

### Phase 4: Portfolio Spring Boot (16 semaines)
**Période :** Semaines 33-50 (parallèle à Phase 3)
**Objectif :** Applications web modernes et employabilité

| Semaines | Module | Focus Principal | Projet Livré |
|----------|--------|-----------------|---------------|
| 33-35 | Spring Intro | IoC, DI, Spring Boot | REST API Simple |
| 36-38 | Spring Data | JPA, repositories | Employee Management |
| 39-41 | Security & Testing | Spring Security, tests | E-commerce Backend |
| 42-44 | Web & Deployment | Thymeleaf, Docker | Portfolio Website |
| 45-48 | Microservices | Spring Cloud, APIs | Microservices Demo |
| 49-50 | Optimization | Performance, monitoring | Projet final |

**📚 Ressources Gratuites Phase 4 :**
- **🎯 Principal :** [Spring.io Guides](https://spring.io/guides) (tutoriels officiels)
- **📺 Vidéo :** [Java Brains Spring Boot](https://www.youtube.com/playlist?list=PLqq-6Pq4lTTbx8p2oCgcAQGQyqN8XeA1x) (série complète)
- **💻 Pratique :** [Spring Boot Tutorial](https://www.tutorialspoint.com/spring_boot/) (exercices)
- **📖 Documentation :** [Spring Framework Docs](https://docs.spring.io/spring-framework/docs/current/reference/html/) (référence)
- **🧩 Projets :** [Spring Boot Examples](https://github.com/spring-projects/spring-boot/tree/main/spring-boot-samples) (GitHub)
- **📚 Cours :** [Baeldung Spring](https://www.baeldung.com/spring-tutorial) (articles détaillés)

## 📚 Ressources d'Apprentissage

### Cours Principaux
1. **Oracle MyLearn** - Java SE 17 Developer (officiel)
2. **FreeCodeCamp** - Java Full Course (14h vidéo)
3. **MOOC Helsinki** - Java Programming I & II
4. **Baeldung** - Tutorials Java avancés

### Livres de Référence
1. **"OCP Oracle Certified Professional Java SE 17"** - Scott Selikoff
2. **"Effective Java"** - Joshua Bloch
3. **"Java: The Complete Reference"** - Herbert Schildt
4. **"Spring Boot in Action"** - Craig Walls

### Plateformes de Pratique
1. **CodingBat** - Exercices Java fondamentaux
2. **HackerRank** - Algorithmes et structures de données
3. **LeetCode** - Problèmes de programmation
4. **Enthuware** - Examens blancs OCP

### Documentation Officielle
1. **Oracle Java Documentation** - API et guides
2. **Spring Framework Documentation** - Référence complète
3. **JUnit 5 User Guide** - Tests unitaires
4. **Maven Documentation** - Gestion de projet

## ⏰ Rythme d'Apprentissage

### Engagement Hebdomadaire
- **6 heures/semaine** minimum
- **Répartition :** 4h théorie + 2h projet
- **Planning :** 3 sessions de 2h (lun/mer/sam)

### Structure d'une Semaine Type
#### Lundi (2h) - Théorie
- 1h cours vidéo/lecture
- 1h prise de notes et synthèse

#### Mercredi (2h) - Pratique
- 1h exercices CodingBat/HackerRank
- 1h expérimentation code

#### Samedi (2h) - Projet
- 2h développement projet hebdomadaire
- Tests, documentation, Git

### Métriques de Suivi
- **Heures d'étude** - Tracker quotidien
- **Projets complétés** - Checklist progression
- **Tests réussis** - Score examens blancs
- **Commits Git** - Activité développement

## 🎯 Critères de Validation

### Fin de Phase 1 (Semaine 16)
- [x] 7 projets console complétés et documentés
- [x] Maîtrise syntaxe Java complète
- [x] POO : 4 piliers + interfaces
- [x] Collections : List, Set, Map
- [x] Tests JUnit : TDD + couverture 80%+
- [x] Git workflow : branches, commits, releases

### Fin de Phase 2 (Semaine 32)
- [ ] 7 projets Java SE avancés complétés
- [ ] Generics : types, wildcards, PECS
- [ ] Streams : API complète, collectors
- [ ] Concurrence : threads, synchronisation
- [ ] Modules : JPMS, encapsulation
- [ ] Score 70%+ examens blancs OCP

### Fin de Phase 3 (Semaine 44)
- [ ] Certification OCP obtenue (score 80%+)
- [ ] Maîtrise complète Java SE 17
- [ ] Portfolio GitHub professionnel
- [ ] CV technique à jour

### Fin de Phase 4 (Semaine 50)
- [ ] 5 applications Spring Boot déployées
- [ ] Maîtrise Spring ecosystem
- [ ] Projets avec CI/CD
- [ ] Prêt pour emploi développeur Java

## 📊 Suivi et Ajustements

### Révisions Mensuelles
- **Semaine 4, 8, 12, 16** - Bilan Phase 1
- **Semaine 20, 24, 28, 32** - Bilan Phase 2
- **Semaine 36, 40, 44** - Bilan Phase 3
- **Semaine 48, 50** - Bilan Phase 4

### Indicateurs de Réussite
- **Régularité :** 6h/semaine maintenues
- **Qualité :** Projets documentés et testés
- **Progression :** Concepts maîtrisés selon planning
- **Certification :** Score examens blancs croissant

### Plan de Contingence
- **Retard 1-2 semaines :** Réduction scope projet
- **Retard 3-4 semaines :** Extension phase suivante
- **Difficultés majeures :** Révision méthodologie
- **Échec examen :** Nouvelle tentative + renforcement

---

*Plan créé le [Date] - Dernière révision le [Date]*
